<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="NewPassword_BtnTitle" xml:space="preserve">
    <value>Confirmar e-mail</value>
  </data>
  <data name="NewPassword_Help" xml:space="preserve">
    <value>Em caso de dúvida entre em contato com o nosso suporte pelo e-mail:</value>
  </data>
  <data name="NewPassword_LinkDescription" xml:space="preserve">
    <value>Ou acesse o link abaixo:</value>
  </data>
  <data name="NewPassword_RecoverDescriptionEnd" xml:space="preserve">
    <value>é seu, você será direcionado para a definição da sua senha de acesso.</value>
  </data>
  <data name="NewPassword_RecoverDescriptionInitial" xml:space="preserve">
    <value>Após sua confirmação de que o endereço de e-mail</value>
  </data>
  <data name="NewPassword_Welcome" xml:space="preserve">
    <value>Seja bem-vindo(a) ao Boötis!</value>
  </data>
  <data name="NewPassword_Subject" xml:space="preserve">
    <value>{0} Seja bem-vindo(a) ao Boötis!</value>
  </data>
  <data name="ResetPassword_BtnTitle" xml:space="preserve">
    <value>Recuperar senha</value>
  </data>
  <data name="ResetPassword_Disregard" xml:space="preserve">
    <value>Caso você não tenha solicitado a alteração de senha, desconsidere esse e-mail.</value>
  </data>
  <data name="ResetPassword_Help" xml:space="preserve">
    <value>Em caso de dúvida entre em contato com o nosso suporte pelo e-mail:</value>
  </data>
  <data name="ResetPassword_LinkDescription" xml:space="preserve">
    <value>Ou acesse o link abaixo</value>
  </data>
  <data name="ResetPassword_RecoverDescription" xml:space="preserve">
    <value>Para redefinir a sua senha, clique no botão abaixo</value>
  </data>
  <data name="ResetPassword_Required" xml:space="preserve">
    <value>Foi requisitado uma recuperação de senha para o email:</value>
  </data>
  <data name="ResetPassword_Subject" xml:space="preserve">
    <value>Recuperar senha</value>
  </data>
  <data name="EmailTemplate_NotFound" xml:space="preserve">
    <value>O template de e-mail {0} não foi encontrado</value>
  </data>
  <data name="Email_ConfirmeEmail" xml:space="preserve">
    <value>Confirmar Email</value>
  </data>
  <data name="Email_CriarNovaSenha" xml:space="preserve">
    <value>Criar Nova Senha</value>
  </data>
  <data name="Email_NovaSenha" xml:space="preserve">
    <value>Nova Senha</value>
  </data>
  <data name="Email_ResetarSenha" xml:space="preserve">
    <value>Resetar Senha</value>
  </data>
  <data name="Email_Validation" xml:space="preserve">
    <value>Email já está sendo utilizado</value>
  </data>
  <data name="Login_UsuarioSenhaInvalido" xml:space="preserve">
    <value>Usuário ou senha inválidos.</value>
  </data>
  <data name="RefreshToken_TokenInvalido" xml:space="preserve">
    <value>Token não encontrado ou não expirado.</value>
  </data>
  <data name="Usuario_CodigoInvalido" xml:space="preserve">
    <value>Código inválido.</value>
  </data>
  <data name="Usuario_NaoEncontrado" xml:space="preserve">
    <value>Usuário não encontrado.</value>
  </data>
  <data name="Usuario_SenhaInvalida" xml:space="preserve">
    <value>Senha inválida.</value>
  </data>
  <data name="Conglomerado_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum conglomerado com o guid: {0}</value>
  </data>
  <data name="Usuario_EmailJaCadastrado" xml:space="preserve">
    <value>Já existe um usuário com este email.</value>
  </data>
  <data name="EmpresaMatriz_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma empresa matriz para o conglomerado com o Id: {0}</value>
  </data>
  <data name="EmpresaPagadora_Guid_NaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma empresa responsável pelo pagamento com o Guid: {0}</value>
  </data>
  <data name="Empresa_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma empresa com o guid: {0}</value>
  </data>
  <data name="Empresa_NaoEncontrada" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma empresa.</value>
  </data>
  <data name="Grupo_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Grupo com o guid: {0}</value>
  </data>
  <data name="Grupo_NomeJaCadastrado" xml:space="preserve">
    <value>Já existe um Grupo com este nome.</value>
  </data>
  <data name="Usuario_Responsavel" xml:space="preserve">
    <value>Usuario responsável por uma empresa, não pode ser excluido.</value>
  </data>
  <data name="Usuario_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum usuário com o guid: {0}</value>
  </data>
  <data name="Usuario_NenhumLogadoEncontrado" xml:space="preserve">
    <value>Nenhum usuário logado encontrado.</value>
  </data>
  <data name="Usuario_Requerido" xml:space="preserve">
    <value>Usuario Requerido.</value>
  </data>
  <data name="Validation_CampoRequerido" xml:space="preserve">
    <value>O campo {0} é requerido.</value>
  </data>
  <data name="Cnpj_JaCadastrado" xml:space="preserve">
    <value>CNPJ já cadastrado.</value>
  </data>
  <data name="Permissao_Administrativo" xml:space="preserve">
    <value>Administrativo</value>
  </data>
  <data name="Permissao_AdministrativoEmpresas" xml:space="preserve">
    <value>Empresas</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuario" xml:space="preserve">
    <value>Grupos de Usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuario" xml:space="preserve">
    <value>Usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioAlterarStatus" xml:space="preserve">
    <value>Alterar status dos usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioEditarDadosPessoais" xml:space="preserve">
    <value>Editar dados pessoais dos usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioEditarGrupo" xml:space="preserve">
    <value>Editar grupos dos usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioEditarPermissao" xml:space="preserve">
    <value>Editar permissões dos usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioExcluir" xml:space="preserve">
    <value>Excluir usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuariosCadastrar" xml:space="preserve">
    <value>Cadastrar novos usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuariosVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioVerLista" xml:space="preserve">
    <value>Ver lista de usuários</value>
  </data>
  <data name="Permissao_Compras" xml:space="preserve">
    <value>Compras</value>
  </data>
  <data name="Permissao_ComprasNecessidadeComprasVerLista" xml:space="preserve">
    <value>Ver lista necessidade de compras</value>
  </data>
  <data name="Permissao_ComprasNecessidadeCompras" xml:space="preserve">
    <value>Necessidade de compras</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoVerLista" xml:space="preserve">
    <value>Ver lista dos modelos de ordens de manipulação</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos modelos de ordens de manipulação</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoCadastrar" xml:space="preserve">
    <value>Cadastrar novos modelos de ordens de manipulação</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes de modelos ordens de manipulação</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoExcluir" xml:space="preserve">
    <value>Excluir modelos de ordens de manipulação</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoAlterarStatus" xml:space="preserve">
    <value>Alterar Status de modelos ordens de manipulação</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacao" xml:space="preserve">
    <value>Modelo de ordens manipulação</value>
  </data>
  <data name="Permissao_ComprasFornecedores" xml:space="preserve">
    <value>Fornecedores</value>
  </data>
  <data name="Permissao_Estoque" xml:space="preserve">
    <value>Estoque</value>
  </data>
  <data name="Permissao_EstoqueAjusteSaldo" xml:space="preserve">
    <value>Ajuste de Saldo</value>
  </data>
  <data name="Permissao_EstoqueLocaisLotes" xml:space="preserve">
    <value>Locais de Estoque</value>
  </data>
  <data name="Permissao_EstoqueLotes" xml:space="preserve">
    <value>Lotes</value>
  </data>
  <data name="Permissao_EstoqueRastreabilidadeVisualizar" xml:space="preserve">
    <value>Visualizar rastreabilidade dos lotes</value>
  </data>
  <data name="Permissao_EstoqueMovimentacaoVisualizar" xml:space="preserve">
    <value>Visualizar movimento de estoque</value>
  </data>
  <data name="Permissao_EstoquePerdas" xml:space="preserve">
    <value>Perdas</value>
  </data>
  <data name="Permissao_EstoqueTransferencia" xml:space="preserve">
    <value>Transferências</value>
  </data>
  <data name="Permissao_EstoqueGrupoProdutos" xml:space="preserve">
    <value>Grupos de Produtos</value>
  </data>
  <data name="Permissao_EstoqueProdutos" xml:space="preserve">
    <value>Produtos</value>
  </data>
  <data name="Permissao_EstoqueSubgruposProdutos" xml:space="preserve">
    <value>Subgrupos de Produtos</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaEditarDados" xml:space="preserve">
    <value>Editar dados da empresa</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaEditarEndereco" xml:space="preserve">
    <value>Editar endereço da empresa</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaEditarStatus" xml:space="preserve">
    <value>Alterar status da empresa</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaEditarUsuarioResponsavel" xml:space="preserve">
    <value>Editar usuário responsável da empresa</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das empresas</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaVerLista" xml:space="preserve">
    <value>Ver lista de empresas</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosAlterarStatus" xml:space="preserve">
    <value>Alterar status dos grupos de usuários</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosCriar" xml:space="preserve">
    <value>Criar grupos de usuários</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes dos grupos de usuários</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosEditarPermissao" xml:space="preserve">
    <value>Editar permissões dos grupos de usuários</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosExcluir" xml:space="preserve">
    <value>Excluir grupos de usuários</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos grupos de usuário</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosVerLista" xml:space="preserve">
    <value>Ver lista dos grupos de usuários</value>
  </data>
  <data name="Permissao_ComprasFornecedoresAlterarStatus" xml:space="preserve">
    <value>Alterar status dos fornecedores</value>
  </data>
  <data name="Permissao_ComprasFornecedoresCadastrar" xml:space="preserve">
    <value>Cadastrar novos fornecedores</value>
  </data>
  <data name="Permissao_ComprasFornecedoresEditarContatos" xml:space="preserve">
    <value>Editar contatos dos fornecedores</value>
  </data>
  <data name="Permissao_ComprasFornecedoresEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes dos fornecedores</value>
  </data>
  <data name="Permissao_ComprasFornecedoresEditarDocumentos" xml:space="preserve">
    <value>Editar documentos dos fornecedores</value>
  </data>
  <data name="Permissao_ComprasFornecedoresEditarEnderecos" xml:space="preserve">
    <value>Editar endereços dos fornecedores</value>
  </data>
  <data name="Permissao_ComprasFornecedoresExcluir" xml:space="preserve">
    <value>Excluir fornecedores</value>
  </data>
  <data name="Permissao_ComprasFornecedoresVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos fornecedores</value>
  </data>
  <data name="Permissao_ComprasFornecedoresVerLista" xml:space="preserve">
    <value>Ver lista de fornecedores</value>
  </data>
  <data name="Permissao_EstoqueAjusteSaldoCadastrar" xml:space="preserve">
    <value>Cadastrar novos ajustes de saldo</value>
  </data>
  <data name="Permissao_EstoqueAjusteSaldoVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos ajustes de saldo</value>
  </data>
  <data name="Permissao_EstoqueAjusteSaldoVerLista" xml:space="preserve">
    <value>Ver lista de ajustes de saldo</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueAlterarStatus" xml:space="preserve">
    <value>Alterar status dos locais de estoque</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueCadastrar" xml:space="preserve">
    <value>Cadastrar novos locais de estoque</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes dos locais de estoque</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueExcluir" xml:space="preserve">
    <value>Excluir locais de estoque</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos locais de estoque</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueVerLista" xml:space="preserve">
    <value>Ver lista de locais de estoque</value>
  </data>
  <data name="Permissao_EstoqueLotesAlterarStatus" xml:space="preserve">
    <value>Alterar status dos lotes</value>
  </data>
  <data name="Permissao_EstoqueLotesCadastrar" xml:space="preserve">
    <value>Cadastrar novos lotes</value>
  </data>
  <data name="Permissao_EstoqueLotesEditarInformacoes" xml:space="preserve">
    <value>Editar informações dos lotes</value>
  </data>
  <data name="Permissao_EstoqueLotesExcluir" xml:space="preserve">
    <value>Excluir lotes</value>
  </data>
  <data name="Permissao_EstoqueLotesVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos lotes</value>
  </data>
  <data name="Permissao_EstoqueLotesVerLista" xml:space="preserve">
    <value>Ver lista de empresas</value>
  </data>
  <data name="Permissao_EstoquePerdasCadastrar" xml:space="preserve">
    <value>Cadastrar novas perdas</value>
  </data>
  <data name="Permissao_EstoquePerdasEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes da perda</value>
  </data>
  <data name="Permissao_EstoquePerdasVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das perdas</value>
  </data>
  <data name="Permissao_EstoquePerdasVerLista" xml:space="preserve">
    <value>Ver lista de perdas</value>
  </data>
  <data name="Permissao_EstoqueTransferenciasCadastrar" xml:space="preserve">
    <value>Cadastrar novas transferências</value>
  </data>
  <data name="Permissao_EstoqueTransferenciasVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das transferências</value>
  </data>
  <data name="Permissao_EstoqueTransferenciasVerLista" xml:space="preserve">
    <value>Ver lista de transferências</value>
  </data>
  <data name="Permissao_EstoqueGruposCadastrar" xml:space="preserve">
    <value>Cadastrar novos grupos de produtos</value>
  </data>
  <data name="Permissao_EstoqueGruposEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes dos grupos de produtos</value>
  </data>
  <data name="Permissao_EstoqueGruposExcluir" xml:space="preserve">
    <value>Excluir grupos de produtos</value>
  </data>
  <data name="Permissao_EstoqueGruposVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos grupos de produtos</value>
  </data>
  <data name="Permissao_EstoqueGruposVerLista" xml:space="preserve">
    <value>Ver lista de grupos de produtos</value>
  </data>
  <data name="Permissao_EstoqueProdutosAlterarStatus" xml:space="preserve">
    <value>Alterar status dos produtos</value>
  </data>
  <data name="Permissao_EstoqueProdutosCadastrar" xml:space="preserve">
    <value>Cadastrar novos produtos</value>
  </data>
  <data name="Permissao_EstoqueProdutosEditarInformacoes" xml:space="preserve">
    <value>Editar informações dos produtos</value>
  </data>
  <data name="Permissao_EstoqueProdutosEditarInformacoesFinanceiras" xml:space="preserve">
    <value>Editar informações financeiras dos produtos</value>
  </data>
  <data name="Permissao_EstoqueProdutosExcluir" xml:space="preserve">
    <value>Excluir produtos</value>
  </data>
  <data name="Permissao_EstoqueProdutosVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos produtos</value>
  </data>
  <data name="Permissao_EstoqueProdutosVerLista" xml:space="preserve">
    <value>Ver lista de produtos</value>
  </data>
  <data name="Permissao_EstoqueSubGruposCadastrar" xml:space="preserve">
    <value>Cadastrar novos subgrupos de produtos</value>
  </data>
  <data name="Permissao_EstoqueSubGruposEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes dos subgrupos de produtos</value>
  </data>
  <data name="Permissao_EstoqueSubGruposExcluir" xml:space="preserve">
    <value>Excluir subgrupos de produtos</value>
  </data>
  <data name="Permissao_EstoqueSubGruposVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos Subgrupos de produtos</value>
  </data>
  <data name="Permissao_EstoqueSubGruposVerLista" xml:space="preserve">
    <value>Ver lista de Subgrupos de produtos</value>
  </data>
  <data name="Permissao_ErroAoAtualizar" xml:space="preserve">
    <value>Não foi possível atualizar a permissão, analise as dependencias.</value>
  </data>
  <data name="AjusteSaldoEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Ajuste Saldo de Estoque com o guid: {0}</value>
  </data>
  <data name="ClassificacaoProduto_GuidNaoEncontrado" xml:space="preserve">
    <value>Nenhuma Classificação de Produto encontrada com o guid: {0}</value>
  </data>
  <data name="ConversaoUnidadeMedida_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível converter a Unidade de Medida Id {0} com a Unidade de Medida de origem Id {1}.</value>
  </data>
  <data name="ConversaUnidadeMedida_UnidadesJaCadastradas" xml:space="preserve">
    <value>Já existe uma conversão de Unidade de Medida cadastrada com as unidades informadas: Origem: {0} Conversão: {1}</value>
  </data>
  <data name="Fornecedor_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar um Fornecedor com o guid: {0}</value>
  </data>
  <data name="Grupo_DescricaoExistente" xml:space="preserve">
    <value>Já existe Grupo com a descrição {0}</value>
  </data>
  <data name="Grupo_ExclusaoProibida" xml:space="preserve">
    <value>O Grupo não pode ser excluído porque está vinculado a {0} produto(s) e {1} subgrupo(s).</value>
  </data>
  <data name="LancamentoPerda_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma Perda com o guid: {0}</value>
  </data>
  <data name="LancamentoPerda_SaldoEstoqueInsuficiente" xml:space="preserve">
    <value>Saldo de estoque insuficiente para a realização da Perda: {0}</value>
  </data>
  <data name="LocalEstoque_DescricaoExistente" xml:space="preserve">
    <value>Já existe um Local de Estoque {0} para a empresa com Id {1}.</value>
  </data>
  <data name="LocalEstoque_EmUso" xml:space="preserve">
    <value>O Local de Estoque {0}, não pode ser excluido pois está relacionado em outras partes do sistema.</value>
  </data>
  <data name="LocalEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Local de Estoque com o guid: {0}</value>
  </data>
  <data name="Lote_DataValidadeVencida" xml:space="preserve">
    <value>Não é possível cadastrar um Lote já vencido.</value>
  </data>
  <data name="Lote_DensidadeInvalida" xml:space="preserve">
    <value>Densidade informada para o Lote inválida.</value>
  </data>
  <data name="Lote_EmUso" xml:space="preserve">
    <value>O Lote {0} já teve movimentação dentro do sistema e por isso não é possível sua exclusão.</value>
  </data>
  <data name="Lote_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Lote com o guid: {0}</value>
  </data>
  <data name="Lote_NaoPossuiQuantidadeDeProduto" xml:space="preserve">
    <value>O Lote {0} não possui a quantidade de Produto {1}</value>
  </data>
  <data name="Lote_NumeroJaExistente" xml:space="preserve">
    <value>O Número de Lote {0} já existe para o Produto.</value>
  </data>
  <data name="Lote_QuantidadeInvalida" xml:space="preserve">
    <value>Quantidade informada para o Lote inválida.</value>
  </data>
  <data name="MotivoPerda_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum motivo de perda com o guid: {0}</value>
  </data>
  <data name="MovimentoEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Movimento de Estoque com o guid: {0}</value>
  </data>
  <data name="OperacaoEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma operação de estoque com o guid: {0}</value>
  </data>
  <data name="Pais_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Pais com guid: {0}</value>
  </data>
  <data name="Produto_DescricaoExistente" xml:space="preserve">
    <value>Já existe outro Produto com a descrição: {0}</value>
  </data>
  <data name="Produto_GuidNaoEncontrado" xml:space="preserve">
    <value>Nenhum Produto encontrado com o guid: {0}</value>
  </data>
  <data name="Produto_GuidNaoPodeSerExcluido" xml:space="preserve">
    <value>O Produto de guid: {0}, não pode ser excluido pois está relacionado em outras partes do sistema.</value>
  </data>
  <data name="Produto_PossuiQuantidadeEmEstoque" xml:space="preserve">
    <value>O Produto {0} tem quantidade em estoque e por isso não é possível desativar o controle de lote.</value>
  </data>
  <data name="SaldoEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Saldo de Estoque com o Lote guid: {0}</value>
  </data>
  <data name="SaldoEstoque_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Saldo de Estoque com o Lote id: {0} e o Local de Estoque id: {1}</value>
  </data>
  <data name="SituacaoLote_Ativo" xml:space="preserve">
    <value>Ativo</value>
  </data>
  <data name="SituacaoLote_Bloqueado" xml:space="preserve">
    <value>Bloqueado</value>
  </data>
  <data name="SituacaoLote_ControleQualidade" xml:space="preserve">
    <value>Controle de Qualidade</value>
  </data>
  <data name="SituacaoLote_Inativo" xml:space="preserve">
    <value>Inativo</value>
  </data>
  <data name="SubGrupo_DescricaoExistente" xml:space="preserve">
    <value>Já existe Subgrupo com a descrição {0} para esse Grupo.</value>
  </data>
  <data name="SubGrupo_ExclusaoProibida" xml:space="preserve">
    <value>O Subgrupo não pode ser excluído porque está vinculado a {0} produto(s).</value>
  </data>
  <data name="SubGrupo_NaoPertenceAoGrupoPrincipal" xml:space="preserve">
    <value>O Subgrupo do guid: {0}, não pertence ao Grupo de guid: {1}</value>
  </data>
  <data name="SubGrupo_NaoPodeSerCadastradoComoGrupoPai" xml:space="preserve">
    <value>Não é possível cadastrar um Subgrupo como GrupoPai.</value>
  </data>
  <data name="UnidadeMedida_AbreviacaoExistente" xml:space="preserve">
    <value>Abreviação já cadastrada {0}.</value>
  </data>
  <data name="UnidadeMedida_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma Unidade de Medida com guid: {0}</value>
  </data>
  <data name="Usuario_IdInvalido" xml:space="preserve">
    <value>Usuário informado, id: {0} não encontrado.</value>
  </data>
  <data name="Validation_CampoInvalido" xml:space="preserve">
    <value>O campo {0} está com valor inválido.</value>
  </data>
  <data name="Validation_TamanhoMaximo" xml:space="preserve">
    <value>O campo {0} tem tamanho máximo {1}.</value>
  </data>
  <data name="TipoCapsula_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum tipo cápsula com id: {0}</value>
  </data>
  <data name="ProdutoSinonimo_GuidNaoEncontrado" xml:space="preserve">
    <value>Nenhum produto sinonimo encontrado com o guid {0}</value>
  </data>
  <data name="ProdutoSinonimo_DescricaoExistente" xml:space="preserve">
    <value>Já existe um sinônimo com a descrição {0}. Guid do sinônimo: {1}</value>
  </data>
  <data name="Produto_NomeSinonimoExistente" xml:space="preserve">
    <value>Já existe Produto/Sinonimo com a descrição {0}</value>
  </data>
  <data name="ProdutoMensagem_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma mensagem de produto com o id: {0}</value>
  </data>
  <data name="Mensagem_GuidNaoEncontrado" xml:space="preserve">
    <value>Nenhuma mensagem de produto encontrada com o guid {0}</value>
  </data>
  <data name="ProdutoMateriaPrima_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum produto matéria-prima com guid: {0}</value>
  </data>
  <data name="PesoMolecularBase_Invalido" xml:space="preserve">
    <value>O peso molecular da base deve ser menor ou igual ao peso molecular do sal.</value>
  </data>
  <data name="CombinacaoPellets_QspExcipienteInvalida" xml:space="preserve">
    <value>A matéria-prima não pode ser marcada como qsp, excipiente ou pellets.</value>
  </data>
  <data name="ProdutoMateriaPrima_NaoEhExcipiente" xml:space="preserve">
    <value>Produto selecionado não é um excipiente.</value>
  </data>
  <data name="ProdutoMateriaPrimaCapsulaPronta_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum produto matéria-prima cápsula pronta com guid: {0}</value>
  </data>
  <data name="ProdutoIncompativel_GuidNaoEncontrado" xml:space="preserve">
    <value>Nenhum produto incompativel encontrado com o guid {0}</value>
  </data>
  <data name="Excipiente_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum excipiente com guid: {0}</value>
  </data>
  <data name="ProdutoMateriaPrima_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum produto matéria-prima com id: {0}</value>
  </data>
  <data name="Excipiente_JaCadastrado" xml:space="preserve">
    <value>Excipiente já cadastrado com as seguintes propriedades: Biofarmaceutica: {0}, Prioridade: {1} </value>
  </data>
  <data name="ProdutoEmbalagem_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum produto embalagem com guid: {0}</value>
  </data>
  <data name="Produto_DosagemInvalida" xml:space="preserve">
    <value>O produto está com dosagem inválida.</value>
  </data>
  <data name="ProdutoDiluido_GuidNaoEncontrado" xml:space="preserve">
    <value>Nenhum produto diluido encontrado com o guid {0}</value>
  </data>
  <data name="ProdutoCapsulaPronta_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum produto cápsula pronta com guid: {0}</value>
  </data>
  <data name="ProdutoAssociado_GuidNaoEncontrado" xml:space="preserve">
    <value>Nenhum produto associado encontrado com o guid {0}</value>
  </data>
  <data name="Cas_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum cas com Id: {0}</value>
  </data>
  <data name="Dcb_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum dcb com Id: {0}</value>
  </data>
  <data name="FormaFarmaceutica_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma forma farmaceutica com guid: {0}</value>
  </data>
  <data name="Posologia_DescricaoExistente" xml:space="preserve">
    <value>Já existe uma posologia cadastrada com a descrição: {0}</value>
  </data>
  <data name="Posologia_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi encontrada nenhuma posologia com o guid {0}</value>
  </data>
  <data name="FormulaPadraoItem_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum item de fórmula padrão com guid: {0}</value>
  </data>
  <data name="Laboratorio_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível localizar o laboratório com Id {0}</value>
  </data>
  <data name="LocalEstoque_DevePertencerAMesmaEmpresa" xml:space="preserve">
    <value>O Local de Estoque com guid: {0} deve pertencer a empresa selecionada.</value>
  </data>
  <data name="FormulaPadrao_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma fórmula padrão com guid: {0}</value>
  </data>
  <data name="FormaFarmaceutica_GuidEstaAtiva" xml:space="preserve">
    <value>Forma farmaceutica com id: {0}, está ativa e não pode ser excluida.</value>
  </data>
  <data name="FormaFarmaceutica_DescricaoExistente" xml:space="preserve">
    <value>Já existe uma forma farmaceutica com a descricão {0}</value>
  </data>
  <data name="EmbalagemClassificacaoFormaFarmaceutica_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar embalagem classificação forma farmacêutica com Guid: {0}</value>
  </data>
  <data name="VinculoEmbalagemFormaFarmaceutica_JaExiste" xml:space="preserve">
    <value>Já existe um vinculo criado entre a Embalagem Classificação {0} com essa Forma Farmacêutica.</value>
  </data>
  <data name="EmbalagemClassificacao_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma classificação de embalagem com guid: {0}</value>
  </data>
  <data name="CapsulaCor_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma cápsula cor com guid: {0}</value>
  </data>
  <data name="Grupo_ExclusaoProibidaSubGrupo" xml:space="preserve">
    <value>O Grupo não pode ser excluído porque está vinculado a {0} subgrupo(s).</value>
  </data>
  <data name="Empresa_RemoverComUsuario" xml:space="preserve">
    <value>Não foi possível remover a empresa com o Id: {0} porque tem {1} usuário(s) vinculados.</value>
  </data>
  <data name="Permissao_AdminSetupTenantExecutar" xml:space="preserve">
    <value>Executa a operação de carga inicial do tenant.</value>
  </data>
  <data name="Permissao_AdminSetupTenantVerIdiomas" xml:space="preserve">
    <value>Ver idiomas disponiveis para uso.</value>
  </data>
  <data name="Permissao_Inexistente" xml:space="preserve">
    <value>Não foi possivel encontrar a permissão com id: {0}</value>
  </data>
  <data name="Empresa_ComDependencia" xml:space="preserve">
    <value>Não foi possível excluir a Empresa com o guid: {0} ela já possui movimentação.</value>
  </data>
  <data name="TransferenciaLoteItem_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma Transferência em Lote Item com o guid: {0}</value>
  </data>
  <data name="TransferenciaLote_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma Transferência em Lote com o guid: {0}</value>
  </data>
  <data name="Empresa_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma empresa com o id: {0}</value>
  </data>
  <data name="FormaFarmaceutica_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma forma farmaceutica com id: {0}</value>
  </data>
  <data name="FornecedorContato_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Contado do Fornecedor com a guid: {0}</value>
  </data>
  <data name="FornecedorContato_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Contado do Fornecedor com a id: {0}</value>
  </data>
  <data name="FornecedorDocumento_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Fornecedor com o Documento com o guid: {0}</value>
  </data>
  <data name="FornecedorDocumento_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Fornecedor com o Documento com o id: {0}</value>
  </data>
  <data name="FornecedorEndereco_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar Fornecedor com o Endereço guid: {0}</value>
  </data>
  <data name="FornecedorEndereco_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar Fornecedor com o Endereço id: {0}</value>
  </data>
  <data name="Fornecedor_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar um Fornecedor com o id: {0}</value>
  </data>
  <data name="Grupo_GuidGrupoPaiNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar um Grupo Pai com o guid: {0}</value>
  </data>
  <data name="Grupo_IdGrupoPaiNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar um Grupo Pai com o id: {0}</value>
  </data>
  <data name="LocalEstoque_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Local de Estoque com o id: {0}</value>
  </data>
  <data name="LocalEstoque_NaoPodeSerIgual" xml:space="preserve">
    <value>O Local de Estoque não pode ser igual.</value>
  </data>
  <data name="LocalEstoque_NaoPossuiOProduto" xml:space="preserve">
    <value>O Local de Estoque não possui o Produto.</value>
  </data>
  <data name="Lote_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Lote com o id: {0}</value>
  </data>
  <data name="Ncm_CodigoNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar o código NCM.</value>
  </data>
  <data name="Ncm_DescricaoNaoEncontrada" xml:space="preserve">
    <value>Não foi possível encontrar a descrição NCM.</value>
  </data>
  <data name="Ncm_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar o Id do NCM.</value>
  </data>
  <data name="NotaFiscalEntradaItem_JaLancado" xml:space="preserve">
    <value>Já existe uma Nota Fiscal de entrada do item lançada.</value>
  </data>
  <data name="NotaFiscalEntradaItem_NaoEncontrado" xml:space="preserve">
    <value>Não foi encontrada a Nota Fiscal de entrada do item.</value>
  </data>
  <data name="NotaFiscalEntradaLote_JaLancado" xml:space="preserve">
    <value>Já existe uma nota Fiscal de entrada do lote.</value>
  </data>
  <data name="NotaFiscalEntradaLote_NaoEncontrado" xml:space="preserve">
    <value>Não foi encontrada a Nota Fiscal de entrada do lote.</value>
  </data>
  <data name="NotaFiscalEntradaLote_QuantidadeDivergente" xml:space="preserve">
    <value>Quantidade divergente na Nota Fiscal de entrada do lote.</value>
  </data>
  <data name="NotaFiscalEntrada_Finalizada" xml:space="preserve">
    <value>Nota Fiscal de entrada finalizada.</value>
  </data>
  <data name="NotaFiscalEntrada_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar Nota Fiscal de entrada com o guid: {0}</value>
  </data>
  <data name="NotaFiscalEntrada_JaLancada" xml:space="preserve">
    <value>Já existe uma Nota Fiscal de entrada lançada.</value>
  </data>
  <data name="OperacaoEstoque_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma operação de estoque com o id: {0}</value>
  </data>
  <data name="PedidoCompraItem_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi encontrado o guid: {0} do Pedido de Compra do item.</value>
  </data>
  <data name="PedidoCompra_Cancelado" xml:space="preserve">
    <value>Pedido de Compra cancelado.</value>
  </data>
  <data name="PedidoCompra_Guid_NaoEncontrado" xml:space="preserve">
    <value>Não foi encontrado o guid {0} do Pedido de Compra.</value>
  </data>
  <data name="PedidoCompra_Reprovado" xml:space="preserve">
    <value>Pedido de Compra reprovado.</value>
  </data>
  <data name="ProdutoTipoCapsula_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi encontrado nenhum produto tipo cápsula com o guid: {0}</value>
  </data>
  <data name="Produto_GuidJaExisteNoPedidoDeCompra" xml:space="preserve">
    <value>Já existe um Produto com a guid: {0} no pedido de compra.</value>
  </data>
  <data name="Produto_IdNaoEncontrado" xml:space="preserve">
    <value>Nenhum Produto encontrado com o id: {0}</value>
  </data>
  <data name="TipoContato_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possivel encontrar nenhum tipo de Contato com o id: {0}</value>
  </data>
  <data name="TipoDocumento_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possivel encontrar nenhum tipo de Documento com o id: {0}</value>
  </data>
  <data name="TipoFornecedor_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possivel encontrar nenhum tipo de Fornecedor com o id: {0}</value>
  </data>
  <data name="TipoFrete_CodigoNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum código do tipo do Frete.</value>
  </data>
  <data name="UnidadeMedida_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma Unidade de Medida com id: {0}</value>
  </data>
  <data name="GrupoProduto_DescricaoExistente" xml:space="preserve">
    <value>Já existe Grupo com a descrição {0}</value>
  </data>
  <data name="GrupoProduto_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Grupo com o guid: {0}</value>
  </data>
  <data name="Usuario_ComDependencias" xml:space="preserve">
    <value>O usuário possui dependências e não pode ser excluído.</value>
  </data>
  <data name="Conglomerado_EmUso" xml:space="preserve">
    <value>O Conglomerado {0}, não pode ser excluido pois está relacionado com outras empresas.</value>
  </data>
  <data name="NaturezaOperacao_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possivel encontrar nenhuma natureza de operação com o id: {0}</value>
  </data>
  <data name="Grupo_ComDependencia" xml:space="preserve">
    <value>Não foi possível excluir o Grupo com o guid: {0} ele já possui movimentação.</value>
  </data>
  <data name="Fornecedor_ComDependencia" xml:space="preserve">
    <value>Não foi possível excluir o Fornecedor com o guid: {0} ele já possui movimentação.</value>
  </data>
  <data name="Lote_BloqueadoOuInativo" xml:space="preserve">
    <value>O Lote {0} está Bloqueado ou Inativo.</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerEstornado" xml:space="preserve">
    <value>Não foi possível estornar o Pedido de Compra {0}</value>
  </data>
  <data name="SubGrupo_GuidNaoEncontrado" xml:space="preserve">
    <value>Nenhum Subgrupo encontrado com o guid {0}</value>
  </data>
  <data name="Prescritor_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi encontrado nenhum prescitor com o guid {0}</value>
  </data>
  <data name="TipoRegistro_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possivel encontrar nenhum tipo de registro com o id: {0}</value>
  </data>
  <data name="Prescritor_JaCadastrado" xml:space="preserve">
    <value>Prescritor já cadastrado com a seguinte propriedade: CodigoRegistro: {0}</value>
  </data>
  <data name="UnidadeMedida_Invalida" xml:space="preserve">
    <value>Unidade de medida {0} não pertence a classe do produto.</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerConfirmadoComFornecedor" xml:space="preserve">
    <value>Não foi possível confirmar o Pedido de Compra {0} com o fornecedor.</value>
  </data>
  <data name="Cliente_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi encontrado nenhum cliente com o guid {0}</value>
  </data>
  <data name="Cliente_JaCadastrado" xml:space="preserve">
    <value>Cliente já cadastrado com a seguinte propriedade: Codigo: {0}</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerReprovado" xml:space="preserve">
    <value>Não foi possível reprovar o Pedido de Compra {0}</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerCancelado" xml:space="preserve">
    <value>Não foi possível cancelar o Pedido de Compra {0}</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerAprovado" xml:space="preserve">
    <value>Não foi possível aprovar o Pedido de Compra {0}</value>
  </data>
  <data name="TipoRegistro_JaCadastrado" xml:space="preserve">
    <value>Já existe um {0} com o código {1} e UF {2} cadastrado.</value>
  </data>
  <data name="ClienteDocumento_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum documento do cliente com o id: {0}</value>
  </data>
  <data name="ClienteEndereco_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum endereço do cliente com o id: {0}</value>
  </data>
  <data name="PrescritorEndereco_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar Prescritor com o Endereço guid: {0}</value>
  </data>
  <data name="ClienteDocumento_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Cliente com o Documento com o guid: {0}</value>
  </data>
  <data name="ClienteContato_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Cliente com o Contato com o guid: {0}</value>
  </data>
  <data name="ClienteEndereco_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Cliente com o Endereco com o guid: {0}</value>
  </data>
  <data name="PrescritorContato_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Contado do Prescritor com a guid: {0}</value>
  </data>
  <data name="PrescritorDocumento_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Prescritor com o Documento com o guid: {0}</value>
  </data>
  <data name="Cliente_ComDependencia" xml:space="preserve">
    <value>Não foi possível excluir o Cliente com o guid: {0} ele já possui movimentação.</value>
  </data>
  <data name="Estado_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Estado com guid: {0}</value>
  </data>
  <data name="Cidade_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma Cidade com guid: {0}</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos pedidos de compra</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraVerLista" xml:space="preserve">
    <value>Ver lista dos pedidos de compra</value>
  </data>
  <data name="Permissao_ComprasPedidosCompra" xml:space="preserve">
    <value>Pedidos Compra</value>
  </data>
  <data name="Permissao_AdministrativoManager" xml:space="preserve">
    <value>Acesso as áreas Admin Bootis</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraAprovar" xml:space="preserve">
    <value>Aprovar pedidos de compra</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraCadastrar" xml:space="preserve">
    <value>Cadastrar novos pedidos de compra</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraCancelar" xml:space="preserve">
    <value>Cancelar pedidos de compra</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraConfirmarComFornecedor" xml:space="preserve">
    <value>Confirmar pedidos de compra com o fornecedor</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes dos pedido de compras</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraEstornarConfirmadoComFornecedor" xml:space="preserve">
    <value>Estornar pedidos de compra confirmados com o fornecedor</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraEstornarLiberado" xml:space="preserve">
    <value>Estornar pedidos de compra aprovados</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraExcluir" xml:space="preserve">
    <value>Excluir pedidos de compra</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraReprovar" xml:space="preserve">
    <value>Reprovar pedidos de compra</value>
  </data>
  <data name="Permissao_Vendas" xml:space="preserve">
    <value>Vendas</value>
  </data>
  <data name="Permissao_VendasClientes" xml:space="preserve">
    <value>Clientes</value>
  </data>
  <data name="Permissao_VendasClientesAlterarStatus" xml:space="preserve">
    <value>Alterar status dos clientes</value>
  </data>
  <data name="Permissao_VendasClientesCadastrar" xml:space="preserve">
    <value>Cadastrar novos clientes</value>
  </data>
  <data name="Permissao_VendasClientesEditarContatos" xml:space="preserve">
    <value>Editar contatos dos clientes</value>
  </data>
  <data name="Permissao_VendasClientesEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes dos clientes</value>
  </data>
  <data name="Permissao_VendasClientesEditarDocumentos" xml:space="preserve">
    <value>Editar documentos dos clientes</value>
  </data>
  <data name="Permissao_VendasClientesEditarEnderecos" xml:space="preserve">
    <value>Editar endereços dos clientes</value>
  </data>
  <data name="Permissao_VendasClientesExcluir" xml:space="preserve">
    <value>Excluir clientes</value>
  </data>
  <data name="Permissao_VendasClientesVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos clientes</value>
  </data>
  <data name="Permissao_VendasClientesVerLista" xml:space="preserve">
    <value>Ver lista de clientes</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritor" xml:space="preserve">
    <value>Especialidades Prescritor</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorAlterarStatus" xml:space="preserve">
    <value>Alterar status de especialidades do prescritor</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorCadastrar" xml:space="preserve">
    <value>Cadastrar novas de especialidades do prescritor</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes de especialidades do prescritor</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorExcluir" xml:space="preserve">
    <value>Excluir especialidades do prescritor</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorVerDetalhes" xml:space="preserve">
    <value>Ver detalhes de especialidades do prescritor</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorVerLista" xml:space="preserve">
    <value>Ver lista de especialidades do prescritor</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaAlterarStatus" xml:space="preserve">
    <value>Alterar status das notas ficais de entrada</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaCadastrar" xml:space="preserve">
    <value>Cadastrar novas notas fiscais de entrada</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes das notas fiscais de entrada</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaExcluir" xml:space="preserve">
    <value>Excluir notas fiscais de entrada</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaLancarLotes" xml:space="preserve">
    <value>Lançar lotes das notas fiscais de entrada</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das notas fiscais de entrada</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaVerLista" xml:space="preserve">
    <value>Ver lista das notas fiscais de entrada</value>
  </data>
  <data name="Permissao_ComprasNotasFiscaisEntrada" xml:space="preserve">
    <value>Notas Fiscais de Entrada</value>
  </data>
  <data name="Permissao_VendasPrescritoresAlterarStatus" xml:space="preserve">
    <value>Alterar status de prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritoresCadastrar" xml:space="preserve">
    <value>Cadastrar Prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritoresEditarContatos" xml:space="preserve">
    <value>Editar contatos de prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritoresEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes de prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritoresEditarDocumentos" xml:space="preserve">
    <value>Editar documentos de prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritoresEditarEnderecos" xml:space="preserve">
    <value>Editar endereços de prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritoresExcluir" xml:space="preserve">
    <value>Excluir prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritoresVerDetalhes" xml:space="preserve">
    <value>Ver detalhes de prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritoresVerLista" xml:space="preserve">
    <value>Ver lista de prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritores" xml:space="preserve">
    <value>Prescritores</value>
  </data>
  <data name="Cep_NaoEncontrado" xml:space="preserve">
    <value>O cep: {0} não foi encontrado</value>
  </data>
  <data name="ClienteContato_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum contato do cliente com o id: {0}</value>
  </data>
  <data name="CapsulaCor_DescricaoExistente" xml:space="preserve">
    <value>Já existe outra Cápsula de Cor com a descrição: {0}.</value>
  </data>
  <data name="EmbalagemClassificacao_DescricaoExistente" xml:space="preserve">
    <value>Já existe outra Classificação Embalagem com a descrição: {0}.</value>
  </data>
  <data name="Prescritor_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum prescritor com o id: {0}</value>
  </data>
  <data name="Laboratorio_DescricaoExistente" xml:space="preserve">
    <value>Já existe um Laboratorio cadastrado com a descricao {0} para essa Empresa.</value>
  </data>
  <data name="ProdutoLoteEmUso_JaAssociado" xml:space="preserve">
    <value>Lote já informado em uso para esse produto e local de estoque.</value>
  </data>
  <data name="ProdutoLoteEmUso_NaoExiste" xml:space="preserve">
    <value>Não foi possível encontrar esse lote associado com o produto.</value>
  </data>
  <data name="Permissao_Producao" xml:space="preserve">
    <value>Produção</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCor" xml:space="preserve">
    <value>Cor de cápsula</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorCadastrar" xml:space="preserve">
    <value>Cadastrar novas cores de cápsulas</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes das cores de cápsulas</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorExcluir" xml:space="preserve">
    <value>Excluir cores de cápsulas</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das cores de cápsulas</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorVerLista" xml:space="preserve">
    <value>Ver lista das cores de cápsulas</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemAlterarStatus" xml:space="preserve">
    <value>Alterar status das classificações de embalagem</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemCadastrar" xml:space="preserve">
    <value>Cadastrar novas classificações de embalagem</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes das classificações de embalagem</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemExcluir" xml:space="preserve">
    <value>Excluir classificações de embalagem</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das classificações de embalagem</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemVerLista" xml:space="preserve">
    <value>Ver lista das classificações de embalagem</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagem" xml:space="preserve">
    <value>Classificação embalagem</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceutica" xml:space="preserve">
    <value>Forma Farmacêutica</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaAlterarStatus" xml:space="preserve">
    <value>Alterar status das formas farmacêuticas</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaCadastrar" xml:space="preserve">
    <value>Cadastrar novas formas farmacêuticas</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes das formas farmacêuticas</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaExcluir" xml:space="preserve">
    <value>Excluir formas farmacêuticas</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das formas farmacêuticas</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaVerLista" xml:space="preserve">
    <value>Ver lista das formas farmacêuticas</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadrao" xml:space="preserve">
    <value>Fórmula Padrão</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoCadastrar" xml:space="preserve">
    <value>Cadastrar novas fórmulas padrão</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes das fórmulas padrão</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoExcluir" xml:space="preserve">
    <value>Excluir fórmulas padrão</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das fórmulas padrão</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoVerLista" xml:space="preserve">
    <value>Ver lista das fórmulas padrão</value>
  </data>
  <data name="Permissao_ProducaoLaboratorio" xml:space="preserve">
    <value>Laboratório</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioAlterarStatus" xml:space="preserve">
    <value>Alterar status dos laboratórios</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioCadastrar" xml:space="preserve">
    <value>Cadastrar novos laboratórios</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes dos laboratórios</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioExcluir" xml:space="preserve">
    <value>Excluir laboratórios</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos laboratórios</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioVerLista" xml:space="preserve">
    <value>Ver lista dos laboratórios</value>
  </data>
  <data name="Permissao_ProducaoPosologia" xml:space="preserve">
    <value>Posologia</value>
  </data>
  <data name="Permissao_ProducaoPosologiaAlterarStatus" xml:space="preserve">
    <value>Alterar status das posologias</value>
  </data>
  <data name="Permissao_ProducaoPosologiaCadastrar" xml:space="preserve">
    <value>Cadastrar novas posologias</value>
  </data>
  <data name="Permissao_ProducaoPosologiaEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes das posologias</value>
  </data>
  <data name="Permissao_ProducaoPosologiaExcluir" xml:space="preserve">
    <value>Excluir posologias</value>
  </data>
  <data name="Permissao_ProducaoPosologiaVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das posologias</value>
  </data>
  <data name="Permissao_ProducaoPosologiaVerLista" xml:space="preserve">
    <value>Ver lista das posologias</value>
  </data>
  <data name="StatusAtendimento_DescricaoExistente" xml:space="preserve">
    <value>Já existe um status de atendimento com a descrição {0}</value>
  </data>
  <data name="StatusAtendimento_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar um status de atendimento com o guid: {0}</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerEditado" xml:space="preserve">
    <value>Não é possível editar o Pedido de Compra {0} com o status {1}</value>
  </data>
  <data name="EspecialidadePrescritor_DescricaoExistente" xml:space="preserve">
    <value>Já existe uma especialidade com a descrição {0}</value>
  </data>
  <data name="EspecialidadePrescritor_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possivel encontrar uma especialidade de prescritor com o guid: {0}</value>
  </data>
  <data name="CapsulaTamanho_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar um tamanho de cápsula com o id: {0}</value>
  </data>
  <data name="EmbalagemAssociacao_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma associação de embalagem com guid: {0}</value>
  </data>
  <data name="EmbalagemCapsulaAssociacao_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma associação de embalagem cápsula com guid: {0}</value>
  </data>
  <data name="Atendimento_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar um atendimento com o guid: {0}</value>
  </data>
  <data name="CstCsosn_NaoEncontrado" xml:space="preserve">
    <value>Cst/Csosn , id: {0} não encontrado.</value>
  </data>
  <data name="TipoDocumento_JaCadastrado" xml:space="preserve">
    <value>Já existe um tipo de documento com o id {0} cadastrado.</value>
  </data>
  <data name="PedidoCompraItem_ValorDescontoInvalido" xml:space="preserve">
    <value>Valor desconto {0} não pode ser maior que o preço {1}</value>
  </data>
  <data name="ProdutoEmbalagem_JaVinculado" xml:space="preserve">
    <value>O Produto {0} já está vinculado a uma Classificação de Embalagem.</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerBaixado" xml:space="preserve">
    <value>Pedido compra {0} não pode ser finalizado.</value>
  </data>
  <data name="Validation_ValorInvalido" xml:space="preserve">
    <value>O valor para {0} deve estar no intervalo entre {1} e {2}.</value>
  </data>
  <data name="FormulaPadrao_ProdutoExistente" xml:space="preserve">
    <value>Já existe uma Formula Padrão para este Produto {0}</value>
  </data>
  <data name="PedidoVendaItem_ValorDescontoInvalido" xml:space="preserve">
    <value>O valor do desconto não pode exceder o valor do item.</value>
  </data>
  <data name="PedidoVenda_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar um pedido de venda com o guid: {0}</value>
  </data>
  <data name="PedidoVenda_NaoPodeSerAprovado" xml:space="preserve">
    <value>Não foi possível aprovar o Pedido de Venda {0}</value>
  </data>
  <data name="PedidoVenda_NaoPodeSerReprovado" xml:space="preserve">
    <value>Não foi possível reprovar o Pedido de Venda {0}</value>
  </data>
  <data name="PedidoVenda_NaoPodeSerEstornado" xml:space="preserve">
    <value>Não foi possível estornar o Pedido de Venda {0}</value>
  </data>
  <data name="Atendimento_PedidoVendaExistente" xml:space="preserve">
    <value>Já existe um Pedido de Venda vinculado a este Atendimento.</value>
  </data>
  <data name="PedidoVenda_ValorTotalInvalido" xml:space="preserve">
    <value>Não é possível inserir um Valor Total maior que o valor total líquido dos itens.</value>
  </data>
  <data name="PedidoVenda_SomaDescontosInvalida" xml:space="preserve">
    <value>Não é permitido inserir um desconto menor que a soma dos descontos unitários dos produtos.</value>
  </data>
  <data name="PedidoVenda_ValorDescontoInvalido" xml:space="preserve">
    <value>O valor do desconto não pode exceder o valor do pedido.</value>
  </data>
  <data name="SaldoInformado_Invalido" xml:space="preserve">
    <value>Saldo informado inválido.</value>
  </data>
  <data name="Permissao_VendasAtendimentosCadastrar" xml:space="preserve">
    <value>Cadastrar novos atendimentos</value>
  </data>
  <data name="Permissao_VendasAtendimentosVerDetalhes" xml:space="preserve">
    <value>Ver detalhes de atendimentos</value>
  </data>
  <data name="Permissao_VendasAtendimentosVerLista" xml:space="preserve">
    <value>Ver lista de atendimentos</value>
  </data>
  <data name="Permissao_VendasPedidosVendaAprovar" xml:space="preserve">
    <value>Aprovar pedidos de venda</value>
  </data>
  <data name="Permissao_VendasPedidosVendaCadastrar" xml:space="preserve">
    <value>Cadastrar novos pedidos de venda</value>
  </data>
  <data name="Permissao_VendasPedidosVendaCancelar" xml:space="preserve">
    <value>Cancelar pedidos de venda</value>
  </data>
  <data name="Permissao_VendasPedidosVendaEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes de pedidos de venda</value>
  </data>
  <data name="Permissao_VendasPedidosVendaEstornar" xml:space="preserve">
    <value>Estornar pedidos de venda</value>
  </data>
  <data name="Permissao_VendasPedidosVendaReprovar" xml:space="preserve">
    <value>Reprovar pedidos de venda</value>
  </data>
  <data name="Permissao_VendasPedidosVendaVerDetalhes" xml:space="preserve">
    <value>Ver detalhes de pedidos de venda</value>
  </data>
  <data name="Permissao_VendasPedidosVendaVerLista" xml:space="preserve">
    <value>Ver lista de pedidos de venda</value>
  </data>
  <data name="Permissao_VendasAtendimentos" xml:space="preserve">
    <value>Atendimentos</value>
  </data>
  <data name="Permissao_VendasPedidosVenda" xml:space="preserve">
    <value>Pedidos de Venda</value>
  </data>
  <data name="NotaFiscalEntrada_NaoPodeSerLancada" xml:space="preserve">
    <value>A Nota Fiscal Entrada {0} tem o status diferente de Pendente e não pode ser lançada.</value>
  </data>
  <data name="VinculoEmbalagemFormaFarmaceutica_Duplicado" xml:space="preserve">
    <value>Não é possível incluir a Embalagem Classificação {0} mais de uma vez na mesma Forma Farmacêutica</value>
  </data>
  <data name="MensagemDescricao_JaExiste" xml:space="preserve">
    <value>Já existe uma mensagem de produto com essa descrição.</value>
  </data>
  <data name="ProdutoMensagem_NaoVinculado" xml:space="preserve">
    <value>Produto não vinculado a mensagem.</value>
  </data>
  <data name="ProdutoMensagem_JaVinculado" xml:space="preserve">
    <value>Produto já vinculado a mensagem.</value>
  </data>
  <data name="NotaFiscalEntradaLote_NaoPodeSerRemovido" xml:space="preserve">
    <value>Os Lotes da Nota Fiscal de Entrada {0} não podem ser excluído, porque já foram lançados. </value>
  </data>
  <data name="PedidoVenda_StatusInvalido" xml:space="preserve">
    <value>Não é possível alterar um pedido de venda com status: {0}.</value>
  </data>
  <data name="ProdutoAssociado_ProdutoInvalido" xml:space="preserve">
    <value>O produto selecionado não se enquadra nas classes aceitas para associar a um produto.</value>
  </data>
  <data name="ProdutoAssociado_VinculoExistente" xml:space="preserve">
    <value>Já existe uma associação criada entre o Produto {0} e o Produto {1} com a Forma Farmacêutica {2}.</value>
  </data>
  <data name="Permissao_EstoqueMensagemProduto" xml:space="preserve">
    <value>Mensagem do Produto</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoCadastrar" xml:space="preserve">
    <value>Cadastrar novas mensagens do produto</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes das mensagens do produto</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoExcluir" xml:space="preserve">
    <value>Excluir mensagens do produto</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoRemoverProduto" xml:space="preserve">
    <value>Remover produto de uma mensagem do produto</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoVerDetalhes" xml:space="preserve">
    <value>Ver detalhes das mensagens do produto</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoVerLista" xml:space="preserve">
    <value>Ver lista das mensagens do produto</value>
  </data>
  <data name="ProdutoDiluido_ProdutoInvalido" xml:space="preserve">
    <value>A classe do produto {1} não é Matéria-Prima e por isso não pode ser diluído.</value>
  </data>
  <data name="ProdutoIncompativel_ProdutoInvalido" xml:space="preserve">
    <value>A classe do produto não é Matéria-Prima e por isso não pode ser incompativel.</value>
  </data>
  <data name="ProdutoIncompativel_VinculoExistente" xml:space="preserve">
    <value>Já existe uma incompatibilidade criada entre o Produto {0} e o Produto {1}.</value>
  </data>
  <data name="ProdutoSinonimo_SinonimoIgualAoProduto" xml:space="preserve">
    <value>Já existe um produto com a descrição {0}. Guid do produto: {1}</value>
  </data>
  <data name="ProdutoSinonimo_ProdutoInvalido" xml:space="preserve">
    <value>A classe do produto não é Matéria-Prima e por isso não é possível cadastrar um sinônimo.</value>
  </data>
  <data name="FormaFarmaceutica_ApresentacaoExistente" xml:space="preserve">
    <value>Já existe uma forma farmaceutica com a apresentacão {0}</value>
  </data>
  <data name="Permissao_EstoqueInformacoesTecnicasVisualizar" xml:space="preserve">
    <value>Visualizar informações técnicas</value>
  </data>
  <data name="Permissao_EstoqueProdutosAssociadoCadastrar" xml:space="preserve">
    <value>Cadastrar novos produtos associados</value>
  </data>
  <data name="Permissao_EstoqueProdutosAssociadoEditarDetalhes" xml:space="preserve">
    <value>Editar produtos associados</value>
  </data>
  <data name="Permissao_EstoqueProdutosAssociadoExcluir" xml:space="preserve">
    <value>Excluir produtos associados</value>
  </data>
  <data name="Permissao_EstoqueProdutosAssociadoVisualizar" xml:space="preserve">
    <value>Visualizar produtos associados</value>
  </data>
  <data name="Permissao_EstoqueProdutosDiluidoCadastrar" xml:space="preserve">
    <value>Cadastrar novos produtos diluídos</value>
  </data>
  <data name="Permissao_EstoqueProdutosDiluidoEditarDetalhes" xml:space="preserve">
    <value>Editar produtos diluídos</value>
  </data>
  <data name="Permissao_EstoqueProdutosDiluidoExcluir" xml:space="preserve">
    <value>Excluir produtos diluídos</value>
  </data>
  <data name="Permissao_EstoqueProdutosDiluidoVisualizar" xml:space="preserve">
    <value>Visualizar produtos diluídos</value>
  </data>
  <data name="Permissao_EstoqueProdutosIncompativelCadastrar" xml:space="preserve">
    <value>Cadastrar produtos incompatíveis</value>
  </data>
  <data name="Permissao_EstoqueProdutosIncompativelEditarDetalhes" xml:space="preserve">
    <value>Editar produtos incompatíveis</value>
  </data>
  <data name="Permissao_EstoqueProdutosIncompativelExcluir" xml:space="preserve">
    <value>Excluir produtos incompatíveis</value>
  </data>
  <data name="Permissao_EstoqueProdutosIncompativelVisualizar" xml:space="preserve">
    <value>Visualizar produtos incompatíveis</value>
  </data>
  <data name="Permissao_EstoqueProdutosSinonimoCadastrar" xml:space="preserve">
    <value>Cadastrar novos sinônimos</value>
  </data>
  <data name="Permissao_EstoqueProdutosSinonimoEditarDetalhes" xml:space="preserve">
    <value>Editar sinônimos</value>
  </data>
  <data name="Permissao_EstoqueProdutosSinonimoExcluir" xml:space="preserve">
    <value>Excluir sinônimos</value>
  </data>
  <data name="Permissao_EstoqueProdutosSinonimoVisualizar" xml:space="preserve">
    <value>Visualizar sinônimos</value>
  </data>
  <data name="SaldoEstoqueLocal_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Saldo de Estoque com o Lote guid: {0} e o Local de Estoque guid: {1}</value>
  </data>
  <data name="NotaFiscalEntradaLote_UnidadeMedidaInvalidaParaClasseProduto" xml:space="preserve">
    <value>A UnidadeMedidaId {1} é incompatível com a ClasseProdutoId {2}</value>
  </data>
  <data name="Permissao_EstoqueProdutosMensagemCadastrar" xml:space="preserve">
    <value>Vincular mensagens ao produto</value>
  </data>
  <data name="Permissao_EstoqueProdutosMensagemExcluir" xml:space="preserve">
    <value>Excluir mensagens do produto</value>
  </data>
  <data name="Permissao_EstoqueProdutosMensagemVisualizar" xml:space="preserve">
    <value>Visualizar mensagens do produto</value>
  </data>
  <data name="FormulaPadraoItem_QuantidadeInvalida" xml:space="preserve">
    <value>Quantidade invalida para o Produto.</value>
  </data>
  <data name="Usuario_GrupoRequerido" xml:space="preserve">
    <value>O usuário deve ser atribuído a pelo menos um grupo.</value>
  </data>
  <data name="ProdutoDiluido_DosagemObrigatoria" xml:space="preserve">
    <value>Os campos de dosagem é obrigatório quando a Forma Farmacêutica for nula.</value>
  </data>
  <data name="FormaFarmaceutica_UnidadeMedidaInvalida" xml:space="preserve">
    <value>A Unidade de Medida {0} é inválida no contexto de Forma Farmacêutica.</value>
  </data>
  <data name="ProdutoDiluido_DosagemInvalida" xml:space="preserve">
    <value>Diluição {0} existente com dosagem mínima {1} e dosagem máxima {2} dentro do intervalo de dosagem mínima {3} e dosagem máxima {4} para a unidade de medida {5}.</value>
  </data>
  <data name="Grupo_UsuarioNaoEncontrado" xml:space="preserve">
    <value>Não foi encontrado nenhum usuário com Guid {0} no grupo {1}</value>
  </data>
  <data name="Lote_NaoPossuiInformacaoTecnica" xml:space="preserve">
    <value>O Lote {0} não possui Informações Técnicas.</value>
  </data>
  <data name="Embalagem_NaoDisponivel" xml:space="preserve">
    <value>Não existe embalagem disponível esta configuração da receita.</value>
  </data>
  <data name="Capsula_NaoDisponivel" xml:space="preserve">
    <value>Não existe cápsula disponível esta configuração da receita.</value>
  </data>
  <data name="Conglomerado_NomeJaCadastrado" xml:space="preserve">
    <value>Esse nome já está sendo usado</value>
  </data>
  <data name="ProdutoSinonimo_ProdutoNaoVinculado" xml:space="preserve">
    <value>Não existe um vínculo entre o sinônimo com o guid {0} e o produto com o guid {1}</value>
  </data>
  <data name="Produto_ComMovimentacao" xml:space="preserve">
    <value>O Produto {0} possui movimentação no sistema, por este motivo não é possível mudar a classe do produto.</value>
  </data>
  <data name="ProdutoAssociado_RecursaoDetectada" xml:space="preserve">
    <value>Recursão detectada em produtos associados, verifique seu cadastro.</value>
  </data>
  <data name="Inventario_NaoPodeSerAtualizado" xml:space="preserve">
    <value>Não foi possível editar o Inventário {0}.</value>
  </data>
  <data name="Inventario_NaoPodeSerCancelado" xml:space="preserve">
    <value>Não foi possível cancelar o Inventário {0}.</value>
  </data>
  <data name="Inventario_NaoPodeIniciarLancamento" xml:space="preserve">
    <value>O Inventário {0} não pode dar inicío ao Lançamento pois o status atual não é correspondente a "Aguardando Lançamento".</value>
  </data>
  <data name="SaldoEstoque_SaldoBloqueado" xml:space="preserve">
    <value>Não é possível movimentar o Saldo de Estoque {0}, pois o saldo correspondente está bloqueado.</value>
  </data>
  <data name="ReceitaManipulada_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma receita manipulada com o guid: {0}</value>
  </data>
  <data name="PedidoVenda_NaoPodeSerCancelado" xml:space="preserve">
    <value>Não foi possível cancelar o Pedido de Venda {0}</value>
  </data>
  <data name="Fornecedor_CnpjExistente" xml:space="preserve">
    <value>Já existe outro Fornecedor com o CNPJ {0} cadastrado.</value>
  </data>
  <data name="Fornecedor_CpfExistente" xml:space="preserve">
    <value>Já existe outro Fornecedor com o CPF {0} cadastrado.</value>
  </data>
  <data name="Inventario_NaoPodeFinalizarLancamento" xml:space="preserve">
    <value>Não é possível finalizar o lançamento do Inventário {0} pois o status atual não é correspondente a Lançamento.</value>
  </data>
  <data name="Inventario_InventarioItensIdsDuplicados" xml:space="preserve">
    <value>Não é possível lançar mais de uma vez o mesmo InventarioItemId.</value>
  </data>
  <data name="Inventario_LancamentoComItensFaltando" xml:space="preserve">
    <value>Não foi possível finalizar o lançamento do Inventário porquê há produtos que não foram preenchidos.</value>
  </data>
  <data name="Inventario_LancamentoNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum InventarioLancamento com o código {0} para o Inventário {1} .</value>
  </data>
  <data name="Permissao_EstoqueInventarioCadastrar" xml:space="preserve">
    <value>Cadastrar novos inventários</value>
  </data>
  <data name="Permissao_EstoqueInventarioExcluir" xml:space="preserve">
    <value>Excluir inventários</value>
  </data>
  <data name="Permissao_EstoqueInventarioVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos inventários</value>
  </data>
  <data name="Permissao_EstoqueInventarioVerDetalhesConferencia" xml:space="preserve">
    <value>Ver detalhes da conferência</value>
  </data>
  <data name="Permissao_EstoqueInventarioVerLista" xml:space="preserve">
    <value>Ver lista de inventários</value>
  </data>
  <data name="Permissao_EstoqueInventario" xml:space="preserve">
    <value>Inventários</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipulada" xml:space="preserve">
    <value>Receitas</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaVerLista" xml:space="preserve">
    <value>Ver lista de receitas</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaVerDetalhes" xml:space="preserve">
    <value>Ver detalhes de receitas</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaCadastrar" xml:space="preserve">
    <value>Cadastrar receitas</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes de receitas</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaAlterarStatus" xml:space="preserve">
    <value>Alterar status de receitas</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaExcluir" xml:space="preserve">
    <value>Excluir receitas</value>
  </data>
  <data name="Inventario_NaoPodeFinalizarConferencia" xml:space="preserve">
    <value>Não é possível finalizar o conferência do Inventário {0} pois o status atual não é correspondente a Conferência.</value>
  </data>
  <data name="Inventario_LancamentoNaoPoderSerCancelado" xml:space="preserve">
    <value>O lançamento atual do Inventário {0} não pode ser cancelado.</value>
  </data>
  <data name="Inventario_NaoPodeAdicionarProduto" xml:space="preserve">
    <value>Não é possível adicionar produtos no lançamento atual do Inventário {0} porquê o mesmo não se encontra no status Lançamento.</value>
  </data>
  <data name="Inventario_ProdutoExistenteNoLancamento" xml:space="preserve">
    <value>Lote {0} existente no lançamento atual do Inventário {1}.</value>
  </data>
  <data name="Inventario_LocalEstoqueInexistente" xml:space="preserve">
    <value>O Local Estoque {0} não pertence ao Inventário {1}.</value>
  </data>
  <data name="ReceitaManipuladaItem_IdNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum componente da receita com o id: {0}</value>
  </data>
  <data name="ReceitaManipuladaItem_FatoresNaoEncontrados" xml:space="preserve">
    <value>Não existem fatores para o produto com id {0}</value>
  </data>
  <data name="Inventario_NaoPodeSerExcluido" xml:space="preserve">
    <value>Não é possível excluir o Inventário {0} pois o status atual não é correspondente a Aguardando Lançamento.</value>
  </data>
  <data name="Cliente_CpfExistente" xml:space="preserve">
    <value>Já existe um cliente com o CPF {0} cadastrado.</value>
  </data>
  <data name="Cliente_CnpjExistente" xml:space="preserve">
    <value>Já existe um cliente com o CNPJ {0} cadastrado.</value>
  </data>
  <data name="Empresa_CnpjExistente" xml:space="preserve">
    <value>Existe outra Empresa cadastrada com o CNPJ {0}.</value>
  </data>
  <data name="PedidoVendaItem_ProdutoInativo" xml:space="preserve">
    <value>Não é possível aprovar um pedido de venda com produtos inativos</value>
  </data>
  <data name="Conglomerado_EmpresaNaoVinculada" xml:space="preserve">
    <value>A Empresa {0} não faz parte do Conglomerado {1}.</value>
  </data>
  <data name="ReceitaManipuladaItem_OrdemInvalida" xml:space="preserve">
    <value>Já existe um componente da receita com a ordem {0}.</value>
  </data>
  <data name="SaldoEstoque_NaoEncontradoLoteId" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Saldo de Estoque com o Lote id: {0}</value>
  </data>
  <data name="Inventario_LocalEstoqueNaoVinculado" xml:space="preserve">
    <value>O Local de Estoque {0} não está vinculado ao Inventário {1}.</value>
  </data>
  <data name="ModeloRotulo_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum Modelo de Rótulo com o guid: {0}</value>
  </data>
  <data name="ModeloRotulo_NomeJaExiste" xml:space="preserve">
    <value>Já existe um modelo de rótulo para o tipo escolhido com a descrição {0}</value>
  </data>
  <data name="ReceitaManipulada_QuantidadeReceitaInvalida" xml:space="preserve">
    <value>Quantidade da receita inválida: valor nulo ou zero.</value>
  </data>
  <data name="ReceitaManipulada_ClasseBiofarmaceuticaInvalida" xml:space="preserve">
    <value>Não foi possível determinar a classe biofarmacêutica para as matérias-primas da receita.</value>
  </data>
  <data name="ReceitaManipulada_UnidadeMedidaQspInvalida" xml:space="preserve">
    <value>A unidade de medida do QSP não é válida para esta forma farmacêutica.</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloVerLista" xml:space="preserve">
    <value>Ver lista dos modelos de rótulos</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloVerDetalhes" xml:space="preserve">
    <value>Ver detalhes dos modelos de rótulos</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloCadastrar" xml:space="preserve">
    <value>Cadastrar novos modelos de rótulos</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloEditarDetalhes" xml:space="preserve">
    <value>Editar detalhes dos modelos rótulos</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloExcluir" xml:space="preserve">
    <value>Excluir modelos rótulos</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloAlterarStatus" xml:space="preserve">
    <value>Alterar Status modelos rótulos</value>
  </data>
  <data name="Permissao_ProducaoModeloRotulo" xml:space="preserve">
    <value>Modelo Rótulo</value>
  </data>
  <data name="ReceitaManipuladaItem_DataValidadeNaoEncontrada" xml:space="preserve">
    <value>Não foi possível identificar a data de validade do produto {0}</value>
  </data>
  <data name="ReceitaManipulada_ExcipienteNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum excipiente. Por favor revise seu cadastro.</value>
  </data>
  <data name="ReceitaManipulada_MateriaPrimaNaoEncontrada" xml:space="preserve">
    <value>Não foi possível encontrar nenhuma matéria-prima para o cálculo da receita.</value>
  </data>
  <data name="NotaFiscalEntradaLote_InformacaoTecnicaObrigatorio" xml:space="preserve">
    <value>Informação Técnica obrigatória para o InventárioItemId {0} pois o Produto pertencente é da classe Matéria-Prima</value>
  </data>
  <data name="ProdutoEmbalagem_Invalido" xml:space="preserve">
    <value>O produto informado não é uma embalagem válida</value>
  </data>
  <data name="ProdutoTipoCapsula_Invalido" xml:space="preserve">
    <value>O produto informado não é uma cápsula válida</value>
  </data>
  <data name="ReceitaManipulada_DosagemTipoCalculoInvalido" xml:space="preserve">
    <value>Não é permitido cadastrar quantidade de dose para formas farmacêuticas com tipo de cálculo Receita.</value>
  </data>
  <data name="Produto_GrupoSemProjecaoEstoque" xml:space="preserve">
    <value>A projeção de estoque está desativada para todos os Produtos do Grupo: {0}.</value>
  </data>
  <data name="ReceitaManipuladaItem_FormaFarmaceuticaInvalida" xml:space="preserve">
    <value>O item associado não é compatível com a forma farmacêutica da receita.</value>
  </data>
  <data name="Permissao_EstoqueMovimentacao" xml:space="preserve">
    <value>Movimento de estoque</value>
  </data>
  <data name="Permissao_EstoqueRastreabilidade" xml:space="preserve">
    <value>Rastreabilidade</value>
  </data>
  <data name="ModeloOrdemManipulacao_NomeJaExiste" xml:space="preserve">
    <value>Já existe um modelo de ordem manipulação para o tipo escolhido com a descrição {0}</value>
  </data>
  <data name="ModeloOrdemManipulacao_GuidNaoEncontrado" xml:space="preserve">
    <value>Não foi possível encontrar nenhum modelo de ordem de manipulação com o guid: {0}</value>
  </data>
  <data name="ProjecaoEstoque_IntervaloInvalido" xml:space="preserve">
    <value>O valor de 'Inicio' deve ser maior que o 'Fim' da projeção para formar um intervalo válido</value>
  </data>
  <data name="Produto_EstoqueMinimoInvalido" xml:space="preserve">
    <value>O estoque mínimo não pode ser negativo</value>
  </data>
  <data name="Produto_EstoqueMaximoInvalido" xml:space="preserve">
    <value>O estoque máximo não pode ser negativo</value>
  </data>
  <data name="Produto_EstoqueInvalido" xml:space="preserve">
    <value>O estoque mínimo não pode ser maior que o máximo</value>
  </data>
  <data name="ProjecaoEstoque_DadosIncompletos" xml:space="preserve">
    <value>Por favor, preencha todos os campos de dias de estoque e período de análise.</value>
  </data>
  <data name="NecessidadeCompra_FiltroIncorreto" xml:space="preserve">
    <value>O filtro de Produto não pode ser combinado com outros filtros.</value>
  </data>
  <data name="Permissao_ProducaoOrdemManipulacaoReceitaManipuladaEmitir" xml:space="preserve">
    <value>Emitir ordem de manipulação</value>
  </data>
  <data name="Permissao_ProducaoOrdemManipulacaoReceitaManipuladaVerLista" xml:space="preserve">
    <value>Ver lista de ordens de manipulação</value>
  </data>
  <data name="Permissao_ProducaoRotuloReceitaManipuladaEditarDetalhes" xml:space="preserve">
    <value>Editar rótulo de receita</value>
  </data>
  <data name="Permissao_ProducaoRotuloReceitaManipuladaEmitir" xml:space="preserve">
    <value>Emitir rótulo de receita</value>
  </data>
  <data name="Permissao_ProducaoRotuloReceitaManipuladaVerLista" xml:space="preserve">
    <value>Ver lista de rótulos de receita</value>
  </data>
  <data name="Permissao_ProducaoOrdemManipulacaoReceitaManipulada" xml:space="preserve">
    <value>Emissão de Ordem de Manipulação</value>
  </data>
  <data name="Permissao_ProducaoRotuloReceitaManipulada" xml:space="preserve">
    <value>Emissão de Rótulo de Receita</value>
  </data>
  <data name="Permissao_EstoqueProjecaoVisualizar" xml:space="preserve">
    <value>Visualizar projeção de estoque</value>
  </data>
  <data name="Permissao_EstoqueProjecao" xml:space="preserve">
    <value>Projeção de estoque</value>
  </data>
  <data name="ReceitaManipulada_StatusInvalido" xml:space="preserve">
    <value>Não é possível alterar uma Receita Manipulada com o Status {0}</value>
  </data>
  <data name="ProdutoEmbalagem_ModeloRotuloAssociacaoExistenteParaTodasAsFormasFarmaceuticas" xml:space="preserve">
    <value>Não é possível vincular dois ou mais Modelos de Rótulo para todas as Formas Farmacêuticas.</value>
  </data>
  <data name="ProdutoEmbalagem_ModeloRotuloDuplicadoParaFormaFarmaceutica" xml:space="preserve">
    <value>Não é possível vincular dois ou mais Modelos de Rótulo para a mesma Forma Farmacêutica.</value>
  </data>
</root>