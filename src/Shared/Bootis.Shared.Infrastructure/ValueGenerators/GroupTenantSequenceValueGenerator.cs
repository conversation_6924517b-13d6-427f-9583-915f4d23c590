using Bootis.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.ValueGeneration;

namespace Bootis.Shared.Infrastructure.ValueGenerators;

public class GroupTenantSequenceValueGenerator : ValueGenerator<int>
{
    public override bool GeneratesTemporaryValues => false;

    public override int Next(EntityEntry entry)
    {
        var context = (BootisContext)entry.Context;
        return !entry.CurrentValues.TryGetValue("GroupTenantId", out Guid groupTenantId)
            ? -1
            : context.GetNextSequence(entry.Entity.GetType().Name, groupTenantId, Guid.Empty);
    }
}