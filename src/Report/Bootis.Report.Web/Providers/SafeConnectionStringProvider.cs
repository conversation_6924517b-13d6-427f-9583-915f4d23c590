using DevExpress.Data.Entity;

namespace Bootis.Report.Web.Providers;

public class SafeConnectionStringProvider : IConnectionStringsProvider
{
    public IConnectionStringInfo[] GetConnections()
    {
        return [];
    }

    public IConnectionStringInfo[] GetConfigFileConnections()
    {
        return [];
    }

    public IConnectionStringInfo GetConnectionStringInfo(string connectionStringName)
    {
        return null;
    }

    public string GetConnectionString(string connectionStringName)
    {
        return null;
    }
}