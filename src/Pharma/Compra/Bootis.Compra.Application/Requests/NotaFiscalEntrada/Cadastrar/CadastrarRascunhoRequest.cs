using Bootis.Compra.Domain.Dtos.NotaFiscalEntrada;
using Bootis.Shared.Common.DTOs.Responses;
using MediatR;

namespace Bootis.Compra.Application.Requests.NotaFiscalEntrada.Cadastrar;

public class CadastrarRascunhoRequest : IRequest<CadastrarGlobalResponse>, INotaFiscalEntradaRascunhoDto
{
    public Guid FornecedorId { get; set; }
    public Guid? TransportadoraId { get; set; }
    public Guid NaturezaOperacaoId { get; set; }
    public Guid TipoFreteId { get; set; }
    public IEnumerable<Guid> PedidosId { get; set; }
    public IEnumerable<NotaFiscalEntradaItemRascunhoDto> Itens { get; set; }
    public int Numero { get; set; }
    public int Serie { get; set; }
    public DateOnly DataEmissao { get; set; }
    public DateOnly DataEntrega { get; set; }
    public decimal? ValorBaseIcms { get; set; }
    public decimal? ValorIcms { get; set; }
    public decimal? ValorBaseIcmsSubstituicao { get; set; }
    public decimal? ValorIcmsSubstituicao { get; set; }
    public decimal? ValorFrete { get; set; }
    public decimal? ValorSeguro { get; set; }
    public decimal? ValorDesconto { get; set; }
    public decimal? ValorOutrasDespesas { get; set; }
    public decimal? ValorIpi { get; set; }
    public string InformacoesComplementares { get; set; }
}