using Asp.Versioning;
using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Atualizar;
using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Cadastrar;
using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Listar;
using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Obter;
using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Remover;
using Bootis.Shared.Api.Attributes;
using Bootis.Shared.Common;
using Bootis.Shared.Common.DTOs.Responses;
using Bootis.Shared.Common.Query;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Compra.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Compras")]
[Route("compras/v{version:apiVersion}/[controller]")]
public class NotaFiscalEntradaController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(typeof(CadastrarGlobalResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_Cadastrar)]
    public async Task<IActionResult> Cadastrar(CadastrarRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpPost]
    [Route("Rascunho")]
    [ProducesResponseType(typeof(CadastrarGlobalResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_Cadastrar)]
    public async Task<IActionResult> CadastrarRascunho(CadastrarRascunhoRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpPut]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_EditarDetalhes)]
    public async Task<IActionResult> Atualizar(AtualizarRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("Rascunho")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_EditarDetalhes)]
    public async Task<IActionResult> AtualizarRascunho(AtualizarRascunhoRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpDelete]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_Excluir)]
    public async Task<IActionResult> Remover(RemoverRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpDelete]
    [Route("RemoverPedido")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_Excluir)]
    public async Task<IActionResult> RemoverPedido(RemoverPedidoNotaRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPost]
    [Route("Lancar")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_LancarLotes)]
    public async Task<IActionResult> LancarLote(LancarNotaFiscalEntradaRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpPost]
    [Route("Lote/Rascunho")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_LancarLotes)]
    public async Task<IActionResult> CadastrarLoteRascunho(CadastrarLoteRascunhoRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpPut]
    [Route("Lote/Rascunho")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AtualizarLoteRascunho(AtualizarLoteRascunhoRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpGet]
    [Route("ListarDetalhado")]
    [ProducesResponseType(typeof(PaginatedResult<ListarDetalhadoResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_VerLista)]
    public async Task<IActionResult> ListarDetalhado([FromQuery] ListarDetalhadoRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarHistorico")]
    [ProducesResponseType(typeof(PaginatedResult<ListarHistoricoResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_VerLista)]
    public async Task<IActionResult> ListarHistorico([FromQuery] ListarHistoricoRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_VerDetalhes)]
    public async Task<IActionResult> Obter(Guid id)
    {
        var request = new ObterRequest { Id = id };

        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("{id:Guid}/Lote")]
    [ProducesResponseType(typeof(ObterLoteResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Compras_NotaFiscalEntrada_VerDetalhes)]
    public async Task<IActionResult> ListarLote(Guid id)
    {
        var request = new ObterLoteRequest
        {
            Id = id
        };

        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpDelete]
    [Route("{id:Guid}/Item/{itemId:Guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RemoverItem(Guid id, Guid itemId)
    {
        var command = new RemoverItemRequest
            { NotaFiscalEntradaId = id, NotaFiscalEntradaItemId = itemId };

        await mediator.Send(command);

        return Ok();
    }

    [HttpDelete]
    [Route("{id:Guid}/Lote/Rascunho")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RemoverLoteRascunho(Guid id)
    {
        var request = new RemoverLoteRequest { NotaFiscalEntradaId = id };

        await mediator.Send(request);

        return Ok();
    }
}