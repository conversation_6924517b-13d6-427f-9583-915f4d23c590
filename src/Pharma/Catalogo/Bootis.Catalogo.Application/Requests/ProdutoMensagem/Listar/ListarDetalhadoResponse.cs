using System.Runtime.Serialization;

namespace Bootis.Catalogo.Application.Requests.ProdutoMensagem.Listar;

public class ListarDetalhadoResponse
{
    public Guid MensagemId { get; private set; }
    public string MensagemDescricao { get; set; }
    public IEnumerable<string> Exibicoes { get; set; }

    [IgnoreDataMember] public bool ExibeVenda { get; set; }

    [IgnoreDataMember] public bool ExibeRotulagem { get; set; }

    [IgnoreDataMember] public bool ExibeFichaPesagem { get; set; }

    [IgnoreDataMember] public bool ExibeImpressaoRotulo { get; set; }
}