using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Catalogo.Infrastructure.EntityConfigurations;

public class FormaFarmaceuticaEntityTypeConfiguration : BaseEntityTypeConfiguration<FormaFarmaceutica>
{
    public override void Configure(EntityTypeBuilder<FormaFarmaceutica> builder)
    {
        builder.ToTable("formas_farmaceutica");

        builder
            .Property(c => c.Descricao)
            .NomeDescricao(TamanhoTexto.Cinquenta)
            .IsRequired();

        builder
            .Property(c => c.TipoCalculo)
            .IsRequired();

        builder
            .Property(c => c.Ativo)
            .HasDefaultValue(true)
            .IsRequired();

        builder
            .Property(c => c.Ordem)
            .IsRequired();

        builder
            .Property(c => c.PercentualMinimoExcipiente)
            .ManipulacaoPrecisao()
            .IsRequired();

        // TODO - Ajustar vindo do laboratiro
        // builder
        //     .HasOne(c => c.Laboratorio)
        //     .WithMany()
        //     .HasForeignKey(c => c.LaboratorioId)
        //     .IsRequired()
        //     .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.UsoFormaFarmaceutica)
            .IsRequired();

        builder
            .Property(c => c.Apresentacao)
            .Abreviacao(TamanhoTexto.Cinco)
            .IsRequired();

        builder
            .Property(c => c.ValidadeDias)
            .IsRequired();

        builder
            .Property(c => c.DataCadastro)
            .DataHora()
            .IsRequired();

        builder
            .Property(c => c.CustoOperacional)
            .Financeiro()
            .IsRequired();

        builder
            .Property(c => c.UnidadeMedidaId)
            .IsRequired();

        base.Configure(builder);
    }
}