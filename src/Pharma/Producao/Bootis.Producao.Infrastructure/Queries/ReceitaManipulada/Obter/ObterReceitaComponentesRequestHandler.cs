using System.Data;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;
using Bootis.Producao.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Obter;

public class
    ObterReceitaComponentesRequestHandler(
        IDbConnection connection) : IRequestHandler<ObterReceitaComponentesRequest,
    ObterReceitaComponentesResponse>
{
    public async Task<ObterReceitaComponentesResponse> Handle(ObterReceitaComponentesRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT rm.id,
                                  cl.id AS paciente_id,
                                  pr.id AS prescritor_id,
                                  ff.id AS forma_farmaceutica_id,
                                  rm.quantidade_receita,
                                  ff.apresentacao AS forma_farmaceutica_apresentacao,
                                  rm.quantidade_repetir
                             FROM receitas_manipuladas rm
                                  LEFT JOIN clientes cl ON cl.id = rm.paciente_id
                                  LEFT JOIN prescritores pr ON pr.id = rm.prescritor_id
                                  LEFT JOIN formas_farmaceutica ff ON ff.id = rm.forma_farmaceutica_id
                            WHERE rm.id = @id;

                           """;

        const string sqlComponentes = """
                                      SELECT
                                          ri.id AS componente_id,
                                          ri.descricao_produto,
                                          ri.quantidade,
                                          ri.unidade_medida_id,
                                          un.abreviacao AS unidade_medida_abreviacao,
                                          p.id AS produto_id,
                                          p.classe_produto_id AS classe_produto,
                                          ri.ordem,
                                          ri.oculta_rotulo,
                                          fp.id AS formula_padrao_id
                                      FROM receitas_manipuladas_item ri
                                           LEFT JOIN receitas_manipuladas rm ON rm.id = ri.receita_manipulada_id
                                           LEFT JOIN unidades_medida un ON un.id = ri.unidade_medida_id
                                           LEFT JOIN produtos p ON p.id = ri.produto_id
                                           LEFT JOIN formulas_padrao fp ON fp.produto_id = p.id
                                      WHERE rm.id = @id;
                                      """;

        var response =
            await connection.QuerySingleOrDefaultAsync<ObterReceitaComponentesResponse>(sql,
                new { id = request.Id });

        if (response is null)
        {
            var message = Localizer.Instance.GetMessage_ReceitaManipulada_GuidNaoEncontrado(request.Id);
            throw new DomainException(message);
        }

        var componentes =
            await connection.QueryAsync<ObterComponentesResponse>(sqlComponentes,
                new { id = request.Id });

        response.Componentes = componentes;

        return response;
    }
}