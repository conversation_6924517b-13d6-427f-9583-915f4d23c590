using System.ComponentModel;
using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Application.Requests.ModeloOrdemManipulacao.Listar;

public class ListarResponse
{
    public Guid Id { get; set; }
    public string Descricao { get; set; }
    public TipoOrdemManipulacao TipoOrdemManipulacao { get; set; }

    public virtual string TipoOrdemManipulacaoDescricao
    {
        get
        {
            var descriptionAttribute = typeof(TipoOrdemManipulacao)
                    .GetField(TipoOrdemManipulacao.ToString())
                    .GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];

            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : TipoOrdemManipulacao.ToString();
        }
    }

    public TipoCadastro TipoCadastro { get; set; }

    public virtual string TipoCadastroDescricao
    {
        get
        {
            var descriptionAttribute = typeof(TipoCadastro)
                    .GetField(TipoCadastro.ToString())
                    .GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];

            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : TipoCadastro.ToString();
        }
    }

    public string FormaFarmaceuticaDescricao { get; set; }
    public string LaboratorioDescricao { get; set; }
    public bool Ativo { get; set; }
}