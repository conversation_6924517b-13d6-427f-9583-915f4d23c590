using System.ComponentModel;
using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Listar;

public class ListarDetalhadoRotuloResponse
{
    public int ReceitaNumero { get; set; }
    public string Descricao { get; set; }
    public int PedidoVendaNumero { get; set; }
    public Guid PedidoVendaId { get; set; }
    public StatusVenda PedidoVendaStatusId { get; set; }

    public virtual string PedidoVendaStatusDescricao
    {
        get
        {
            var descriptionAttribute = typeof(StatusVenda)
                    .GetField(PedidoVendaStatusId.ToString())
                    .GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];

            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : PedidoVendaStatusId.ToString();
        }
    }

    public string CanalAtendimentoIcon { get; set; }
    public string PacienteNomeCompleto { get; set; }
    public Guid PacienteId { get; set; }
    public string ModeloRotuloDescricao { get; set; }
    public Guid? ModeloRotuloId { get; set; }
    public DateTime DataEntrega { get; set; }
    public DateTime DataEmissaoReceita { get; set; }
    public StatusImpressao StatusImpressaoId { get; set; }
}