using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAssociadoAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Estoque.Domain.Services;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Domain.Services.ReceitaManipulada;
using Bootis.Producao.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

public class CalculoReceitaManipulada
{
    private readonly IReceitaManipuladaDomainService _domainService;
    private readonly Dictionary<Guid, Guid> _formulas = new();

    private readonly List<(ProdutoAssociado Associado, decimal QuantidadeFinal,
        ReceitaManipuladaRastreioCalculo RastreioOrigem)> _listaProdutosAssociadosTrabalho;

    private readonly HashSet<Guid> _listaProdutosProcessadosTrabalho;
    private readonly ILoteSeletorService _loteSeletorService;
    private readonly Guid? _prescritorIdEntrada;
    private readonly decimal _quantidadeAlvoCalculo;
    private readonly TipoDesconto? _tipoDescontoManualEntrada;

    public CalculoReceitaManipulada(
        ICollection<ReceitaManipuladaItem> itensEntrada,
        decimal quantidadeAlvoCalculo,
        FormaFarmaceutica formaFarmaceuticaEntrada,
        Cliente pacienteEntrada,
        Guid? prescritorIdEntrada,
        TipoDesconto? tipoDescontoManualEntrada,
        decimal? descontoManualEntrada,
        decimal? percentualDescontoManualEntrada,
        IReceitaManipuladaDomainService domainService,
        ILoteSeletorService loteSeletorService)
    {
        Itens = itensEntrada ?? throw new ArgumentNullException(nameof(itensEntrada));
        if (quantidadeAlvoCalculo <= 0)
            throw new ArgumentOutOfRangeException(nameof(quantidadeAlvoCalculo),
                "QuantidadeReceita deve ser maior que zero.");
        _quantidadeAlvoCalculo = quantidadeAlvoCalculo;
        FormaFarmaceutica = formaFarmaceuticaEntrada ??
                            throw new ArgumentNullException(nameof(formaFarmaceuticaEntrada));
        Paciente = pacienteEntrada;
        _prescritorIdEntrada = prescritorIdEntrada;
        _tipoDescontoManualEntrada = tipoDescontoManualEntrada;
        ValorDescontoManual = descontoManualEntrada;
        PercentualDescontoManual = percentualDescontoManualEntrada;
        _domainService = domainService ?? throw new ArgumentNullException(nameof(domainService));

        _listaProdutosAssociadosTrabalho = new List<(ProdutoAssociado, decimal, ReceitaManipuladaRastreioCalculo)>();
        _listaProdutosProcessadosTrabalho = new HashSet<Guid>();
        _loteSeletorService = loteSeletorService ?? throw new ArgumentNullException(nameof(loteSeletorService));
    }

    public virtual IReadOnlyDictionary<Guid, Guid> FormulasPadraoPorProdutoId => _formulas;

    public Guid? PrescritorId => _prescritorIdEntrada;
    public TipoDesconto? TipoDescontoManual => _tipoDescontoManualEntrada;
    public decimal? ValorDescontoManual { get; private set; }

    public decimal? PercentualDescontoManual { get; private set; }

    public TipoTarjaMedicamento? TarjaReceitaIdResultado { get; private set; }

    public async Task ExecutarAsync()
    {
        switch (FormaFarmaceutica.TipoCalculo)
        {
            case TipoCalculo.Receita:
                await CalculoBaseReceitaAsync();
                break;
            case TipoCalculo.QSP:
                await CalculoBaseQspAsync();
                break;
            default:
                throw new InvalidOperationException("Tipo de cálculo desconhecido.");
        }
    }

    private void AdicionarCalculo(
        ReceitaManipuladaRastreioCalculo rastreio,
        decimal quantidade,
        int unidadeMedidaId,
        decimal volume,
        decimal valorCusto,
        decimal valorVenda)
    {
        var fatores = rastreio.Fatores;

        var calculo = new ReceitaManipuladaCalculo(
            rastreio,
            rastreio.Produto.Id,
            rastreio.LoteId,
            quantidade,
            unidadeMedidaId,
            fatores.FatorEquivalencia,
            fatores.FatorFornecedor,
            fatores.FatorDiluicaoInterna,
            fatorConcentracaoAgua: fatores.FatorConcentracaoAgua,
            densidade: fatores.Densidade,
            fatorTotal: fatores.FatorTotal,
            quantidadeVolume: volume,
            unidadeMedidaVolumeId: rastreio.UnidadeMedidaVolumeId ?? unidadeMedidaId,
            valorCusto: valorCusto,
            valorVenda: valorVenda,
            margemLucro: valorVenda - valorCusto,
            fatorSinonimo: fatores.FatorSinonimo,
            fatorCorrecao: fatores.FatorCorrecao
        );

        Calculos.Add(calculo);
    }
    
    public static (int dias, DateTime terminoUtc) CalcularReSell(
        TipoCalculoUsoContinuo? tipoUsoContinuo,
        decimal? quantidadeBase,
        PeriodosPosologia? periodicidade,
        decimal? quantidadeDosePorPeriodo = null
    )
    {
        if (!tipoUsoContinuo.HasValue || !periodicidade.HasValue || !quantidadeBase.HasValue)
            return (0, DateTime.UtcNow);

        var fatorPeriodo = periodicidade.Value switch
        {
            PeriodosPosologia.Dia    => 1,
            PeriodosPosologia.Semana => 7,
            PeriodosPosologia.Mes    => 30,
            _ => 1
        };

        var dias = 0;

        switch (tipoUsoContinuo.Value)
        {
            case TipoCalculoUsoContinuo.PorDose:
                if (quantidadeDosePorPeriodo is null or <= 0)
                    return (0, DateTime.UtcNow);

                dias = (int)Math.Floor((quantidadeBase.Value / quantidadeDosePorPeriodo.Value) * fatorPeriodo);
                break;

            case TipoCalculoUsoContinuo.PorDuracao:
                dias = (int)Math.Floor(quantidadeBase.Value * fatorPeriodo);
                break;

            default:
                return (0, DateTime.UtcNow);
        }

        if (dias < 0) dias = 0;
        var terminoUtc = DateTime.UtcNow.AddDays(dias);
        return (dias, terminoUtc);
    }

    private static void AtualizarValoresFinanceiros(ValoresFinanceiros acumulador,
        ReceitaManipuladaRastreioCalculo rastreio, decimal quantidade)
    {
        CalculadorFinanceiro.AtualizarValoresAcumulados(acumulador, rastreio, quantidade);
    }

    private async Task CalculoBaseReceitaAsync()
    {
        var volumeTotalMateriaPrimaMl = 0m;
        var dataValidadeLocal = DateOnly.MaxValue;
        var regraDataValidadeLocal = RegraDataValidade.Nenhuma;
        var biofarmaceuticaCount = new Dictionary<TipoClasseBiofarmaceutica, int>();
        var hasExcipienteItemUsuario = false;
        var regraEscolhaExcipienteLocal = RegraEscolhaExcipiente.NaoDefinido;
        var valoresAcumulados = new ValoresFinanceiros();
        var tarjaReceitaLocal = TipoTarjaMedicamento.SemTarja;

        if (Itens.All(i => i.Produto.ProdutoMateriaPrima is null && i.Produto.ProdutoTipoCapsula is null))
            throw new DomainException(Localizer.Instance.GetMessage_ReceitaManipulada_MateriaPrimaNaoEncontrada());

        foreach (var item in Itens)
        {
            if (item.Produto.ProdutoMateriaPrima?.IsExcipiente == true)
            {
                hasExcipienteItemUsuario = true;
                item.OcultarRotulo(true);
                regraEscolhaExcipienteLocal = RegraEscolhaExcipiente.ExcipienteEspecifico;
            }

            await AdicionarRastreioPorItemAsync(item);
        }

        await ConsolidarRastreiosAssociadosAsync();

        foreach (var rastreio in Rastreios.Where(r => r.GeraCalculo))
        {
            tarjaReceitaLocal = DeterminarTarjaReceita(tarjaReceitaLocal,
                rastreio.Produto.TipoTarjaMedicamentoId.GetValueOrDefault());

            var resultadoProcessamentoItem = await ProcessarItemBaseReceitaAsync(rastreio, biofarmaceuticaCount);

            volumeTotalMateriaPrimaMl += resultadoProcessamentoItem.VolumeTotalMateriaPrimaMl;
            if (resultadoProcessamentoItem.DataValidade < dataValidadeLocal)
            {
                dataValidadeLocal = resultadoProcessamentoItem.DataValidade;
                regraDataValidadeLocal = resultadoProcessamentoItem.RegraDataValidade;
            }

            if (resultadoProcessamentoItem.RegraEscolhaExcipiente != RegraEscolhaExcipiente.NaoDefinido)
                regraEscolhaExcipienteLocal = resultadoProcessamentoItem.RegraEscolhaExcipiente;

            valoresAcumulados.ValorCustoTotal += resultadoProcessamentoItem.ValorCustoItem;
            valoresAcumulados.ValorVendaTotal += resultadoProcessamentoItem.ValorVendaItem;
            valoresAcumulados.ValorLucroTotal +=
                resultadoProcessamentoItem.ValorVendaItem - resultadoProcessamentoItem.ValorCustoItem;
        }

        TarjaReceitaIdResultado = tarjaReceitaLocal;

        TipoClasseBiofarmaceutica? classeBiofarmaceuticaComum = null;
        if (!hasExcipienteItemUsuario && regraEscolhaExcipienteLocal != RegraEscolhaExcipiente.ExcipienteEspecifico)
        {
            regraEscolhaExcipienteLocal = RegraEscolhaExcipiente.ClasseBioFarmaceutica;
            if (biofarmaceuticaCount.Any())
                classeBiofarmaceuticaComum = DeterminarClasseBiofarmaceuticaComum(biofarmaceuticaCount);
        }

        ResultadoCapsula resultadoCapsulaFinal;
        decimal volumeExcipienteMinimoCalculado = 0;
        decimal volumeTotalCalculado = 0;

        var itemTipoCapsulaUsuario = Itens.FirstOrDefault(i => i.Produto.ProdutoTipoCapsula != null);

        if (itemTipoCapsulaUsuario != null)
        {
            resultadoCapsulaFinal = CalcularCapsulaFornecida(itemTipoCapsulaUsuario, volumeTotalMateriaPrimaMl);
            volumeTotalCalculado = resultadoCapsulaFinal.DivisaoDose *
                                   resultadoCapsulaFinal.Capsula.CapsulaTamanho.VolumeMl * _quantidadeAlvoCalculo;
            volumeExcipienteMinimoCalculado = resultadoCapsulaFinal.QuantidadeExcipiente * _quantidadeAlvoCalculo;
        }
        else
        {
            var volumesCalculados = await CalcularVolumesEscolherCapsulaAsync(volumeTotalMateriaPrimaMl);
            volumeExcipienteMinimoCalculado = volumesCalculados.VolumeExcipienteMinimo;
            volumeTotalCalculado = volumesCalculados.VolumeTotal;
            resultadoCapsulaFinal = volumesCalculados.ResultadoCapsula;
        }

        var capsulaParaAdicionar = resultadoCapsulaFinal.Capsula;
        var quantidadeExcipienteCalculada = resultadoCapsulaFinal.QuantidadeExcipiente * _quantidadeAlvoCalculo;

        if (quantidadeExcipienteCalculada > 0 && !hasExcipienteItemUsuario)
            await AdicionarExcipienteAsync(classeBiofarmaceuticaComum, quantidadeExcipienteCalculada, valoresAcumulados,
                dataValidadeLocal);

        await AdicionarCapsulaEmbalagemAsync(capsulaParaAdicionar, valoresAcumulados, resultadoCapsulaFinal);

        await CriarBaseCalculoAsync(volumeTotalMateriaPrimaMl, regraEscolhaExcipienteLocal, dataValidadeLocal,
            regraDataValidadeLocal,
            volumeExcipienteMinimoCalculado, volumeTotalCalculado, resultadoCapsulaFinal.DivisaoDose);

        await CalcularValoresFinaisAsync(valoresAcumulados.ValorCustoTotal, valoresAcumulados.ValorVendaTotal,
            valoresAcumulados.ValorLucroTotal);
    }

    private static TipoTarjaMedicamento DeterminarTarjaReceita(TipoTarjaMedicamento atual, TipoTarjaMedicamento novo)
    {
        return (TipoTarjaMedicamento)Math.Max((int)atual, (int)novo);
    }

    private async Task CalculoBaseQspAsync()
    {
        var quantidadePercentualSomada = 0m;
        var quantidadeQspCalculada = 0m;
        var quantidadeFinalReceita = ConversaoUnidadeMedidaCreator
            .Criar(FormaFarmaceutica.UnidadeMedidaId, UnidadeMedidaAbreviacao.mL)
            .CalcularConversao(_quantidadeAlvoCalculo);

        var volumeTotalMl = 0m;
        var regraDataValidadeLocal = RegraDataValidade.Nenhuma;
        var dataValidadeLocal = DateOnly.MaxValue;
        var valoresAcumulados = new ValoresFinanceiros();
        var tarjaReceitaLocal = TipoTarjaMedicamento.SemTarja;

        var hasExcipienteItemUsuario = false;
        var regraEscolhaExcipienteLocal = RegraEscolhaExcipiente.NaoDefinido;

        if (Itens.All(i => i.Produto.ProdutoMateriaPrima is null))
            throw new DomainException(Localizer.Instance.GetMessage_ReceitaManipulada_MateriaPrimaNaoEncontrada());

        foreach (var item in Itens)
        {
            if (item.Produto.ProdutoMateriaPrima?.IsExcipiente == true)
            {
                hasExcipienteItemUsuario = true;
                item.OcultarRotulo(true);
                regraEscolhaExcipienteLocal = RegraEscolhaExcipiente.ExcipienteEspecifico;
            }

            await AdicionarRastreioPorItemAsync(item);
        }

        await ConsolidarRastreiosAssociadosAsync();

        var biofarmaceuticaCount = new Dictionary<TipoClasseBiofarmaceutica, int>();

        foreach (var rastreio in Rastreios.Where(r => r.GeraCalculo))
        {
            tarjaReceitaLocal = DeterminarTarjaReceita(tarjaReceitaLocal,
                rastreio.Produto.TipoTarjaMedicamentoId.GetValueOrDefault());

            var quantidadeConvertida = ConversaoUnidadeMedidaCreator
                .Criar((UnidadeMedidaAbreviacao)rastreio.UnidadeMedidaVolumeId.GetValueOrDefault(),
                    UnidadeMedidaAbreviacao.mL)
                .CalcularConversao(rastreio.QuantidadeVolume.GetValueOrDefault());

            quantidadePercentualSomada += quantidadeConvertida;

            var resultadoProcessamento = await ProcessarItemBaseQspAsync(rastreio, quantidadeConvertida);

            AtualizarVolumeEValidadeLocais(resultadoProcessamento, ref volumeTotalMl, ref dataValidadeLocal,
                out regraDataValidadeLocal);

            decimal quantidadeEmEstoque;
            if (rastreio.UnidadeMedidaId == UnidadeMedidaAbreviacao.PER.ToInt())
            {
                var valorConvertidoMl = rastreio.Quantidade / 100m * _quantidadeAlvoCalculo;
                quantidadeEmEstoque = ConverterParaUnEstoque(valorConvertidoMl, UnidadeMedidaAbreviacao.mL.ToInt(),
                    rastreio.Produto);
            }
            else
            {
                quantidadeEmEstoque =
                    ConverterParaUnEstoque(rastreio.Quantidade, rastreio.UnidadeMedidaId, rastreio.Produto);
            }

            AtualizarValoresFinanceiros(valoresAcumulados, rastreio, quantidadeEmEstoque);

            if (rastreio.Produto.ProdutoMateriaPrima?.IsQsp != true)
                ContarClassesBiofarmaceuticas(rastreio.Produto.ProdutoMateriaPrima, biofarmaceuticaCount);
        }

        if (!hasExcipienteItemUsuario) quantidadeQspCalculada = quantidadeFinalReceita - quantidadePercentualSomada;

        TipoClasseBiofarmaceutica? classePredominante = null;

        if (!hasExcipienteItemUsuario && regraEscolhaExcipienteLocal != RegraEscolhaExcipiente.ExcipienteEspecifico)
        {
            regraEscolhaExcipienteLocal = RegraEscolhaExcipiente.ClasseBioFarmaceutica;

            if (biofarmaceuticaCount.Any())
                classePredominante = DeterminarClasseBiofarmaceuticaComum(biofarmaceuticaCount);
        }

        if (quantidadeQspCalculada > 0 && !hasExcipienteItemUsuario)
        {
            if (classePredominante == null)
                throw new DomainException("Não foi possível determinar a classe biofarmacêutica predominante.");

            await AdicionarExcipienteAsync(classePredominante, quantidadeQspCalculada, valoresAcumulados,
                dataValidadeLocal);
            volumeTotalMl += quantidadeQspCalculada;
        }

        TarjaReceitaIdResultado = tarjaReceitaLocal;

        var embalagensFracionadas = await _domainService.EscolherEmbalagensQspFracionadoAsync(volumeTotalMl);

        foreach (var (produtoEmbalagem, quantidade) in embalagensFracionadas)
        {
            var produto = produtoEmbalagem.Produto;

            var rastreio = await AdicionarReceitaRastreioAsync(
                produto,
                null,
                TipoOrigem.Embalagem,
                null,
                quantidade,
                UnidadeMedidaAbreviacao.un.ToInt(),
                true,
                null,
                null);

            AtualizarValoresFinanceiros(valoresAcumulados, rastreio, quantidade);

            var (valorCustoProduto, valorVendaProduto) = CalculadorFinanceiro.CalcularValoresItem(rastreio, quantidade);

            AdicionarCalculo(
                rastreio,
                quantidade,
                UnidadeMedidaAbreviacao.un.ToInt(),
                produto.ProdutoEmbalagem.Volume!.Value,
                valorCustoProduto,
                valorVendaProduto);

            foreach (var associacao in produtoEmbalagem.EmbalagemAssociacao)
            {
                var produtoAssoc = associacao.ProdutoEmbalagemAssociada.Produto;
                var volumeAssoc = associacao.ProdutoEmbalagemAssociada.Volume!.Value;
                var quantidadeAssoc = associacao.QuantidadeEmbalagem!.Value * quantidade;

                var rastreioAssoc = await AdicionarReceitaRastreioAsync(
                    produtoAssoc,
                    null,
                    TipoOrigem.Associado,
                    rastreio,
                    quantidade,
                    UnidadeMedidaAbreviacao.un.ToInt(),
                    true,
                    null,
                    null);

                AtualizarValoresFinanceiros(valoresAcumulados, rastreioAssoc, quantidadeAssoc);

                var (valorCustoAssocQsp, valorVendaAssocQsp) = CalculadorFinanceiro.CalcularValoresItem(rastreioAssoc, quantidadeAssoc);

                AdicionarCalculo(
                    rastreioAssoc,
                    quantidadeAssoc,
                    UnidadeMedidaAbreviacao.un.ToInt(),
                    volumeAssoc,
                    valorCustoAssocQsp,
                    valorVendaAssocQsp);
            }
        }

        await CriarBaseCalculoAsync(
            quantidadeFinalReceita,
            regraEscolhaExcipienteLocal,
            dataValidadeLocal,
            regraDataValidadeLocal,
            quantidadeQspCalculada,
            volumeTotalMl,
            1);

        await CalcularValoresFinaisAsync(
            valoresAcumulados.ValorCustoTotal,
            valoresAcumulados.ValorVendaTotal,
            valoresAcumulados.ValorLucroTotal);
    }

    private static void AtualizarVolumeEValidadeLocais(ProcessamentoResultadoBaseQsp resultado,
        ref decimal volumeTotalMl,
        ref DateOnly dataValidadeLocal,
        out RegraDataValidade regraDataValidadeLocal)
    {
        volumeTotalMl += resultado.VolumeItemMl;

        if (resultado.DataValidade < dataValidadeLocal)
        {
            dataValidadeLocal = resultado.DataValidade;
            regraDataValidadeLocal = resultado.RegraDataValidade;
        }
        else
        {
            regraDataValidadeLocal = RegraDataValidade.Nenhuma;
        }
    }

    private async Task<ReceitaManipuladaRastreioCalculo> AdicionarReceitaRastreioAsync(
        Produto produto,
        ReceitaManipuladaItem itemOrigem,
        TipoOrigem tipoOrigem,
        ReceitaManipuladaRastreioCalculo rastreioOrigem,
        decimal quantidadeInformada,
        int unidadeMedidaId,
        bool geraCalculo,
        bool? desmembra,
        FormulaPadraoDesmembramento? tipoDesmembramento,
        bool ehFormula = false,
        bool? acumula = null,
        decimal? dosagemMaxima = null,
        decimal? dosagemMinima = null,
        int? unidadeMedidaDosagemId = null)
    {
        ReceitaManipuladaRastreioCalculo rastreioFinal = null;

        var unidadeConvertida = unidadeMedidaId;
        var quantidade = quantidadeInformada;

        if (unidadeConvertida == UnidadeMedidaAbreviacao.PER.ToInt())
        {
            quantidade = quantidade / 100m * _quantidadeAlvoCalculo;
            unidadeConvertida = UnidadeMedidaAbreviacao.mL.ToInt();
        }

        var saldoVolume = produto.ProdutoMateriaPrima is not null
            ? ConverterParaMl(quantidade, unidadeConvertida)
            : 0;

        var resultadoSelecao = await _loteSeletorService.ResolverConsumoDeLotesAsync(
            produto,
            FormaFarmaceutica.Id,
            (UnidadeMedidaAbreviacao)unidadeMedidaId,
            quantidade
        );

        foreach (var lote in resultadoSelecao.LotesConsumidos)
        {
            var fatores =
                await _domainService.ObterFatoresTotaisAsync(produto, itemOrigem?.Produto, itemOrigem, lote.LoteInfo);

            if (fatores is not { FatorTotal: > 0, Densidade: > 0 })
                continue;

            var saldoConvertido = ConverterParaMl(lote.LoteInfo.Saldo, lote.LoteInfo.UnidadeMedidaId)
                * fatores.FatorTotal / fatores.Densidade;

            var qtdPrescrita = lote.QuantidadeConsumida;
            var volumeUsado = Math.Min(saldoConvertido, saldoVolume);

            saldoVolume -= volumeUsado;

            rastreioFinal = CriarRastreio(
                produto, itemOrigem, tipoOrigem, rastreioOrigem,
                lote.LoteId, qtdPrescrita, unidadeMedidaId,
                volumeUsado, geraCalculo, fatores,
                desmembra, tipoDesmembramento,
                ehFormula, acumula, dosagemMaxima, dosagemMinima, unidadeMedidaDosagemId
            );

            Rastreios.Add(rastreioFinal);
        }

        if (rastreioFinal == null)
        {
            var fatoresFinais = await _domainService.ObterFatoresTotaisAsync(produto, itemOrigem?.Produto, itemOrigem);
            var qtdPrescritaRestante = quantidade;

            rastreioFinal = CriarRastreio(
                produto, itemOrigem, tipoOrigem, rastreioOrigem,
                null, qtdPrescritaRestante, unidadeMedidaId,
                saldoVolume, geraCalculo, fatoresFinais,
                desmembra, tipoDesmembramento,
                ehFormula, acumula, dosagemMaxima, dosagemMinima, unidadeMedidaDosagemId
            );

            Rastreios.Add(rastreioFinal);
        }

        return rastreioFinal;
    }

    private static ReceitaManipuladaRastreioCalculo CriarRastreio(
        Produto produto,
        ReceitaManipuladaItem itemOrigem,
        TipoOrigem tipoOrigem,
        ReceitaManipuladaRastreioCalculo rastreioOrigem,
        Guid? loteId,
        decimal quantidadePrescrita,
        int unidadeMedidaId,
        decimal volumeUsado,
        bool geraCalculo,
        ReceitaManipuladaRastreioCalculo.FatoresTotais fatores,
        bool? desmembra,
        FormulaPadraoDesmembramento? tipoDesmembramento,
        bool ehFormula,
        bool? acumula,
        decimal? dosagemMaxima,
        decimal? dosagemMinima,
        int? unidadeMedidaDosagemId)
    {
        return new ReceitaManipuladaRastreioCalculo(
            produto,
            itemOrigem,
            tipoOrigem,
            rastreioOrigem,
            loteId,
            quantidadePrescrita,
            unidadeMedidaId,
            volumeUsado,
            (int)UnidadeMedidaAbreviacao.mL,
            geraCalculo,
            fatores,
            desmembra,
            tipoDesmembramento)
        {
            EhFormula = ehFormula,
            Associados = new ReceitaManipuladaRastreioCalculo.ProdutosAssociados(
                acumula,
                dosagemMaxima,
                dosagemMinima,
                unidadeMedidaDosagemId
            )
        };
    }

    private async Task<ProcessamentoResultadoBaseReceita> ProcessarItemBaseReceitaAsync(
        ReceitaManipuladaRastreioCalculo rastreio,
        Dictionary<TipoClasseBiofarmaceutica, int> biofarmaceuticaCount)
    {
        var isTipoCapsula = rastreio.Produto.ClasseProdutoId == TipoClasseProdutoAbreviacao.TipoCapsula;
        var isMateriaPrima = rastreio.Produto.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima;

        var dataValidadeFinalItem = DateOnly.MaxValue;
        var regraValidadeFinalItem = RegraDataValidade.Nenhuma;
        var regraEscolhaExcipienteItem = RegraEscolhaExcipiente.NaoDefinido;

        var (dataValidadeServico, regraServico) =
            await _domainService.RegraDataValidadeItemAsync(rastreio.Produto, FormaFarmaceutica);
        dataValidadeFinalItem = AtualizarDataValidade(dataValidadeServico, regraServico, dataValidadeFinalItem,
            regraValidadeFinalItem,
            out regraValidadeFinalItem);

        if (_quantidadeAlvoCalculo == 0)
            throw new DomainException(Localizer.Instance.GetMessage_ReceitaManipulada_QuantidadeReceitaInvalida());

        if (isMateriaPrima)
        {
            var quantidadeNaUnidadeEstoque =
                ConverterParaUnEstoque(rastreio.Quantidade, rastreio.UnidadeMedidaId, rastreio.Produto);
            var (valorCustoItem, valorVendaItem) = CalculadorFinanceiro.CalcularValoresItem(rastreio, quantidadeNaUnidadeEstoque);

            AdicionarCalculo(
                rastreio,
                quantidadeNaUnidadeEstoque,
                rastreio.Produto.UnidadeEstoqueId.ToInt(),
                rastreio.QuantidadeVolume.Value,
                valorCustoItem,
                valorVendaItem);

            if (rastreio.Produto.ProdutoMateriaPrima != null)
                ContarClassesBiofarmaceuticas(rastreio.Produto.ProdutoMateriaPrima, biofarmaceuticaCount);

            return new ProcessamentoResultadoBaseReceita(
                rastreio.QuantidadeVolume.GetValueOrDefault(),
                dataValidadeFinalItem,
                regraValidadeFinalItem,
                regraEscolhaExcipienteItem,
                valorCustoItem,
                valorVendaItem
            );
        }

        if (isTipoCapsula)
        {
            var (valorCustoItem, valorVendaItem) = CalculadorFinanceiro.CalcularValoresItem(rastreio, rastreio.Quantidade);
            AdicionarCalculo(rastreio, rastreio.Quantidade, rastreio.UnidadeMedidaId, 0, valorCustoItem,
                valorVendaItem);

            return new ProcessamentoResultadoBaseReceita(
                0m,
                dataValidadeFinalItem,
                regraValidadeFinalItem,
                regraEscolhaExcipienteItem,
                valorCustoItem,
                valorVendaItem
            );
        }

        throw new NotImplementedException("Cálculo não implementado para este tipo de item");
    }

    private async Task<ProcessamentoResultadoBaseQsp> ProcessarItemBaseQspAsync(
        ReceitaManipuladaRastreioCalculo rastreio, decimal quantidadeItemCalculadaMl)
    {
        var (dataValidadeItem, regra) =
            await _domainService.RegraDataValidadeItemAsync(rastreio.Produto, FormaFarmaceutica);
        var dataValidade = AtualizarDataValidade(dataValidadeItem, regra, DateOnly.MaxValue, RegraDataValidade.Nenhuma,
            out var regraDataValidade);

        var (valorCustoItem, valorVendaItem) = CalculadorFinanceiro.CalcularValoresItem(rastreio, quantidadeItemCalculadaMl);

        AdicionarCalculo(rastreio, quantidadeItemCalculadaMl, UnidadeMedidaAbreviacao.mL.ToInt(),
            quantidadeItemCalculadaMl, valorCustoItem,
            valorVendaItem);

        return new ProcessamentoResultadoBaseQsp(
            quantidadeItemCalculadaMl,
            valorCustoItem,
            valorVendaItem,
            dataValidade,
            regraDataValidade
        );
    }

    private async Task AdicionarRastreioPorItemAsync(ReceitaManipuladaItem item)
    {
        if (item.Produto.ProdutoTipoCapsula != null)
        {
            item.OcultarRotulo(true);

            var rastreio = await AdicionarReceitaRastreioAsync(
                item.Produto,
                item,
                TipoOrigem.TipoCapsula,
                null,
                _quantidadeAlvoCalculo,
                UnidadeMedidaAbreviacao.un.ToInt(),
                true,
                false,
                null
            );

            await AdicionarRastreiosProdutosAssociadosAsync(rastreio);
            return;
        }

        var formula = await _domainService.ObterFormulaPadraoAsync(item.Produto.Id);
        if (formula != null)
        {
            var desmembrar = formula.FormulaPadraoDesmembramento == FormulaPadraoDesmembramento.DesmembraMesmaFicha;
            var tipoDesmembramento = formula.FormulaPadraoDesmembramento;

            var quantidadeFormula = item.Quantidade * _quantidadeAlvoCalculo;

            var rastreioFormula = await AdicionarReceitaRastreioAsync(
                formula.Produto,
                item,
                TipoOrigem.Original,
                null,
                quantidadeFormula,
                item.UnidadeMedidaId,
                true,
                desmembrar,
                tipoDesmembramento,
                true
            );

            _formulas[formula.ProdutoId] = formula.Id;
            await AdicionarRastreiosProdutosAssociadosAsync(rastreioFormula);

            foreach (var componente in formula.FormulaPadraoItem)
            {
                var subFormula = await _domainService.ObterFormulaPadraoAsync(componente.Produto.Id);
                if (subFormula != null)
                {
                    await ProcessarSubFormulaAsync(componente, item);
                    continue;
                }

                var geraCalculo = desmembrar && componente.Produto.ProdutoMateriaPrima != null;

                var quantidadeComponente = componente.Quantidade * _quantidadeAlvoCalculo;

                var rastreioComponente = await AdicionarReceitaRastreioAsync(
                    componente.Produto,
                    item,
                    TipoOrigem.Desmembrado,
                    rastreioFormula,
                    quantidadeComponente,
                    componente.UnidadeMedidaId.ToInt(),
                    geraCalculo,
                    null,
                    null
                );

                await AdicionarRastreiosProdutosAssociadosAsync(rastreioComponente);
            }

            return;
        }

        decimal quantidadeItem;

        if (FormaFarmaceutica.TipoCalculo == TipoCalculo.Receita)
            quantidadeItem = item.Quantidade * _quantidadeAlvoCalculo;
        else
            quantidadeItem = item.Quantidade;

        var rastreioItem = await AdicionarReceitaRastreioAsync(
            item.Produto,
            item,
            TipoOrigem.Original,
            null,
            quantidadeItem,
            item.UnidadeMedidaId,
            true,
            null,
            null
        );

        await AdicionarRastreiosProdutosAssociadosAsync(rastreioItem);
    }

    private async Task AdicionarRastreiosProdutosAssociadosAsync(ReceitaManipuladaRastreioCalculo rastreio)
    {
        var associados =
            await _domainService.ObterProdutosAssociadosAsync(rastreio.Produto.Id, FormaFarmaceutica.Id);

        foreach (var associado in associados)
            if (associado.FormaFarmaceuticaId != FormaFarmaceutica.Id)
                throw new DomainException(
                    Localizer.Instance.GetMessage_ReceitaManipuladaItem_FormaFarmaceuticaInvalida());

        foreach (var associado in associados)
        {
            decimal quantidadeCalculada;

            if (associado.UnidadeMedidaQuantidadeAssociada == UnidadeMedidaAbreviacao.PER)
                quantidadeCalculada = associado.TipoRelacaoQuantidade switch
                {
                    TipoRelacaoQuantidade.Formula => associado.QuantidadeAssociada * _quantidadeAlvoCalculo / 100m,
                    TipoRelacaoQuantidade.Produto => associado.QuantidadeAssociada * rastreio.Quantidade / 100m,
                    _ => 0
                };
            else
                quantidadeCalculada = associado.QuantidadeAssociada * _quantidadeAlvoCalculo;

            var dosagemMin = associado.DosagemMinima;
            var dosagemMax = associado.DosagemMaxima;

            var temFaixa = dosagemMin.HasValue || dosagemMax.HasValue;
            if (temFaixa)
            {
                var quantidadeMl = ConversaoUnidadeMedidaCreator
                    .Criar(associado.UnidadeMedidaQuantidadeAssociada, UnidadeMedidaAbreviacao.mL)
                    .CalcularConversao(quantidadeCalculada);

                var minMl = dosagemMin.HasValue
                    ? ConversaoUnidadeMedidaCreator
                        .Criar(associado.UnidadeMedidaDosagem, UnidadeMedidaAbreviacao.mL)
                        .CalcularConversao(dosagemMin.Value)
                    : 0;

                var maxMl = dosagemMax.HasValue
                    ? ConversaoUnidadeMedidaCreator
                        .Criar(associado.UnidadeMedidaDosagem, UnidadeMedidaAbreviacao.mL)
                        .CalcularConversao(dosagemMax.Value)
                    : decimal.MaxValue;

                if (quantidadeMl < minMl || quantidadeMl > maxMl)
                    continue;
            }

            _listaProdutosAssociadosTrabalho.Add((associado, quantidadeCalculada, rastreio));
        }
    }

    private async Task ProcessarSubFormulaAsync(FormulaPadraoItem componente, ReceitaManipuladaItem itemPrincipal)
    {
        if (!_listaProdutosProcessadosTrabalho.Add(componente.Produto.Id))
            return;

        var subFormula = await _domainService.ObterFormulaPadraoAsync(componente.Produto.Id);
        if (subFormula is null)
            return;

        var desmembrar = subFormula.FormulaPadraoDesmembramento == FormulaPadraoDesmembramento.DesmembraMesmaFicha;
        var tipoDesmembramento = subFormula.FormulaPadraoDesmembramento;

        var geraCalculoPrincipal = subFormula.Produto.ProdutoMateriaPrima is not null;

        var quantidadeSubFormula = componente.Quantidade * _quantidadeAlvoCalculo;

        var rastreioSubFormula = await AdicionarReceitaRastreioAsync(
            subFormula.Produto,
            itemPrincipal,
            TipoOrigem.Desmembrado,
            null,
            quantidadeSubFormula,
            componente.UnidadeMedidaId.ToInt(),
            geraCalculoPrincipal,
            desmembrar,
            tipoDesmembramento,
            true
        );

        _formulas[subFormula.ProdutoId] = subFormula.Id;

        await AdicionarRastreiosProdutosAssociadosAsync(rastreioSubFormula);

        foreach (var subComponente in subFormula.FormulaPadraoItem)
        {
            var subSubFormula = await _domainService.ObterFormulaPadraoAsync(subComponente.Produto.Id);

            var quantidadeSubComponente = subComponente.Quantidade * _quantidadeAlvoCalculo;

            if (subSubFormula != null)
            {
                await ProcessarSubFormulaAsync(subComponente, itemPrincipal);
            }
            else
            {
                var geraCalculo = desmembrar && subComponente.Produto.ProdutoMateriaPrima is not null;

                var rastreioSubComponente = await AdicionarReceitaRastreioAsync(
                    subComponente.Produto,
                    itemPrincipal,
                    TipoOrigem.Desmembrado,
                    rastreioSubFormula,
                    quantidadeSubComponente,
                    subComponente.UnidadeMedidaId.ToInt(),
                    geraCalculo,
                    null,
                    null
                );

                await AdicionarRastreiosProdutosAssociadosAsync(rastreioSubComponente);
            }
        }
    }

    private async Task ConsolidarRastreiosAssociadosAsync()
    {
        foreach (var grupo in _listaProdutosAssociadosTrabalho.GroupBy(a => a.Associado.ProdutoAssociacao.Id))
        {
            var primeiro = grupo.First();
            var geraCalculo = primeiro.Associado.ProdutoAssociacao.ProdutoMateriaPrima is not null;

            if (primeiro.Associado.Acumula)
            {
                foreach (var associado in grupo)
                    await AdicionarReceitaRastreioAsync(
                        associado.Associado.ProdutoAssociacao,
                        null,
                        TipoOrigem.Associado,
                        associado.RastreioOrigem,
                        associado.QuantidadeFinal,
                        associado.Associado.UnidadeMedidaQuantidadeAssociada.ToInt(),
                        geraCalculo,
                        null,
                        null,
                        false,
                        associado.Associado.Acumula,
                        associado.Associado.DosagemMaxima,
                        associado.Associado.DosagemMinima,
                        associado.Associado.UnidadeMedidaDosagem.ToInt()
                    );
            }
            else
            {
                var maior = grupo.OrderByDescending(g => g.QuantidadeFinal).First();

                await AdicionarReceitaRastreioAsync(
                    maior.Associado.ProdutoAssociacao,
                    null,
                    TipoOrigem.Associado,
                    maior.RastreioOrigem,
                    maior.QuantidadeFinal,
                    maior.Associado.UnidadeMedidaQuantidadeAssociada.ToInt(),
                    geraCalculo,
                    null,
                    null,
                    false,
                    maior.Associado.Acumula,
                    maior.Associado.DosagemMaxima,
                    maior.Associado.DosagemMinima,
                    maior.Associado.UnidadeMedidaDosagem.ToInt()
                );
            }
        }
    }

    private Task CriarBaseCalculoAsync(
        decimal volumeMateriaPrima,
        RegraEscolhaExcipiente regraEscolha,
        DateOnly dataValidade,
        RegraDataValidade regraDataValidade,
        decimal volumeExcipienteMinimo,
        decimal volumeTotal,
        decimal divisaoDose)
    {
        BaseCalculo = new ReceitaManipuladaBaseCalculo(
            0.ToGuid(),
            volumeMateriaPrima,
            volumeExcipienteMinimo,
            volumeTotal,
            regraEscolha,
            dataValidade,
            regraDataValidade,
            divisaoDose
        );

        return Task.CompletedTask;
    }

    private async Task AdicionarExcipienteAsync(TipoClasseBiofarmaceutica? classeComum,
        decimal quantidadeExcipienteMl, ValoresFinanceiros valoresAcumulados,
        DateOnly dataValidadeLimite)
    {
        var excipiente = await _domainService.ObterExcipienteAsync(classeComum.GetValueOrDefault());

        if (excipiente is null)
            throw new DomainException(Localizer.Instance.GetMessage_ReceitaManipulada_ExcipienteNaoEncontrado());

        var produto = excipiente.ProdutoMateriaPrima.Produto;
        var densidade = excipiente.ProdutoMateriaPrima.Densidade.GetValueOrDefault(1);
        var volumeExcipiente = quantidadeExcipienteMl * densidade;

        var rastreio = await AdicionarReceitaRastreioAsync(
            produto,
            null,
            TipoOrigem.Excipiente,
            null,
            quantidadeExcipienteMl,
            UnidadeMedidaAbreviacao.mL.ToInt(),
            true,
            null,
            null);

        var quantidadeEstoque = ConverterParaUnEstoque(volumeExcipiente, UnidadeMedidaAbreviacao.mL.ToInt(), produto);

        AtualizarValoresFinanceiros(valoresAcumulados, rastreio, quantidadeEstoque);

        var (dataValidadeItem, regraItem) =
            await _domainService.RegraDataValidadeItemAsync(produto, FormaFarmaceutica);
        AtualizarDataValidade(dataValidadeItem, regraItem, dataValidadeLimite, RegraDataValidade.Nenhuma, out _);

        var (valorCustoExcipiente, valorVendaExcipiente) = CalculadorFinanceiro.CalcularValoresItem(rastreio, quantidadeEstoque);

        AdicionarCalculo(
            rastreio,
            quantidadeEstoque,
            produto.UnidadeEstoqueId.ToInt(),
            volumeExcipiente,
            valorCustoExcipiente,
            valorVendaExcipiente
        );
    }

    private async Task CalcularValoresFinaisAsync(decimal valorCusto, decimal valorVenda, decimal valorLucro)
    {
        var custoOperacional = FormaFarmaceutica?.CustoOperacional ?? 0m;

        decimal descontoTotal;

        if (_tipoDescontoManualEntrada.HasValue)
        {
            descontoTotal = _tipoDescontoManualEntrada switch
            {
                TipoDesconto.DescontoMonetario => ValorDescontoManual ?? 0m,
                TipoDesconto.DescontoPorcentagem => (PercentualDescontoManual ?? 0m) / 100m *
                                                    (valorVenda + custoOperacional),
                _ => 0m
            };
        }
        else
        {
            var descontoPrescritor = _prescritorIdEntrada.HasValue
                ? await _domainService.ObterDescontoPrescritorAsync(_prescritorIdEntrada.Value)
                : 0m;

            var descontoPaciente = Paciente?.DescontoFormulas ?? 0m;
            var percentualTotal = descontoPrescritor + descontoPaciente;

            descontoTotal = (valorVenda + custoOperacional) * percentualTotal / 100m;
        }

        var precoReceita = valorVenda + custoOperacional;
        var precoVendaTotal = precoReceita - descontoTotal;

        Valores = new ReceitaManipuladaValores(
            0.ToGuid(),
            custoOperacional,
            valorCusto,
            valorLucro,
            precoReceita,
            descontoTotal,
            precoVendaTotal
        );
    }

    private async Task AdicionarCapsulaEmbalagemAsync(
        ProdutoTipoCapsula capsula,
        ValoresFinanceiros valoresAcumulados,
        ResultadoCapsula resultadoCapsula)
    {
        var quantidadeCapsulas = _quantidadeAlvoCalculo * resultadoCapsula.DivisaoDose;

        var rastreioCapsula = await AdicionarReceitaRastreioAsync(
            capsula.Produto,
            null,
            TipoOrigem.TipoCapsula,
            null,
            quantidadeCapsulas,
            UnidadeMedidaAbreviacao.un.ToInt(),
            true,
            null,
            null
        );

        AtualizarValoresFinanceiros(valoresAcumulados, rastreioCapsula, quantidadeCapsulas);

        var (valorCustoCapsula, valorVendaCapsula) = CalculadorFinanceiro.CalcularValoresItem(rastreioCapsula, quantidadeCapsulas);

        AdicionarCalculo(
            rastreioCapsula,
            quantidadeCapsulas,
            UnidadeMedidaAbreviacao.un.ToInt(),
            quantidadeCapsulas,
            valorCustoCapsula,
            valorVendaCapsula
        );

        var embalagem = await _domainService.EscolherEmbalagemAsync(quantidadeCapsulas, capsula.CapsulaTamanho.Id);

        if (embalagem?.Produto?.ProdutoEmbalagem == null)
            throw new DomainException("Produto ou embalagem não encontrada.");

        var capacidadePorEmbalagem = embalagem.Produto.ProdutoEmbalagem.EmbalagemCapsulaTamanhoAssociacao
                                         .FirstOrDefault(e => e.CapsulaTamanhoId == capsula.CapsulaTamanho.Id)
                                         ?.QuantidadeCapsula
                                     ?? throw new DomainException(
                                         "Não foi possível determinar a quantidade de cápsulas por embalagem.");

        var quantidadeEmbalagens = Math.Ceiling(quantidadeCapsulas / capacidadePorEmbalagem);

        var rastreioEmbalagem = await AdicionarReceitaRastreioAsync(
            embalagem.Produto,
            null,
            TipoOrigem.Embalagem,
            null,
            quantidadeEmbalagens,
            UnidadeMedidaAbreviacao.un.ToInt(),
            true,
            null,
            null
        );

        AtualizarValoresFinanceiros(valoresAcumulados, rastreioEmbalagem, quantidadeEmbalagens);

        var (valorCustoEmbalagem, valorVendaEmbalagem) = CalculadorFinanceiro.CalcularValoresItem(rastreioEmbalagem, quantidadeEmbalagens);

        AdicionarCalculo(
            rastreioEmbalagem,
            quantidadeEmbalagens,
            UnidadeMedidaAbreviacao.un.ToInt(),
            embalagem.Produto.ProdutoEmbalagem.Volume!.Value,
            valorCustoEmbalagem,
            valorVendaEmbalagem
        );

        foreach (var assoc in embalagem.EmbalagemAssociacao)
        {
            var produtoAssoc = assoc.ProdutoEmbalagemAssociada.Produto;
            var volumeAssoc = assoc.ProdutoEmbalagemAssociada.Volume!.Value;
            var quantidadeAssoc = assoc.QuantidadeEmbalagem!.Value * quantidadeEmbalagens;

            var rastreioAssoc = await AdicionarReceitaRastreioAsync(
                produtoAssoc,
                null,
                TipoOrigem.Associado,
                rastreioEmbalagem,
                quantidadeEmbalagens,
                UnidadeMedidaAbreviacao.un.ToInt(),
                true,
                null,
                null
            );

            AtualizarValoresFinanceiros(valoresAcumulados, rastreioAssoc, quantidadeAssoc);

            var (valorCustoAssoc, valorVendaAssoc) = CalculadorFinanceiro.CalcularValoresItem(rastreioAssoc, quantidadeAssoc);

            AdicionarCalculo(
                rastreioAssoc,
                quantidadeAssoc,
                UnidadeMedidaAbreviacao.un.ToInt(),
                volumeAssoc,
                valorCustoAssoc,
                valorVendaAssoc
            );
        }
    }

    private static void ContarClassesBiofarmaceuticas(ProdutoMateriaPrima mp,
        Dictionary<TipoClasseBiofarmaceutica, int> contador)
    {
        if (!mp.BiofarmaceuticaId.HasValue) return;
        var classe = mp.BiofarmaceuticaId.Value;
        contador.TryAdd(classe, 0);

        contador[classe]++;
    }

    private TipoClasseBiofarmaceutica DeterminarClasseBiofarmaceuticaComum(
        Dictionary<TipoClasseBiofarmaceutica, int> contador)
    {
        return contador.OrderByDescending(kv => kv.Value).First().Key;
    }

    private async Task<(ResultadoCapsula ResultadoCapsula, decimal VolumeExcipienteMinimo, decimal VolumeTotal)>
        CalcularVolumesEscolherCapsulaAsync(decimal volumeMateriaPrima)
    {
        var resultado = await _domainService.EscolherCapsulaAsync(volumeMateriaPrima);

        var volumeCapsula = resultado.Capsula.CapsulaTamanho.VolumeMl;

        var doseTotal = volumeMateriaPrima / _quantidadeAlvoCalculo;

        var divisaoDose = Math.Ceiling(doseTotal / volumeCapsula);
        var volumeTotalPorDose = volumeCapsula * divisaoDose;

        var excipientePorDose = volumeTotalPorDose - doseTotal;

        if (excipientePorDose < 0)
            throw new DomainException("Nenhuma cápsula comporta a dose. Verifique a fórmula.");

        var volumeExcipienteTotal = excipientePorDose * _quantidadeAlvoCalculo;
        var volumeTotalReceita = volumeTotalPorDose * _quantidadeAlvoCalculo;

        return (
            new ResultadoCapsula(resultado.Capsula, excipientePorDose, divisaoDose),
            VolumeExcipienteMinimo: volumeExcipienteTotal,
            VolumeTotal: volumeTotalReceita
        );
    }

    private static ResultadoCapsula CalcularCapsulaFornecida(
        ReceitaManipuladaItem item,
        decimal volumeMateriaPrimaTotal)
    {
        var capsula = item.Produto.ProdutoTipoCapsula!;
        var volumeCapsula = capsula.CapsulaTamanho.VolumeMl;

        var doseTotal = volumeMateriaPrimaTotal / item.Quantidade;

        var divisaoDose = Math.Ceiling(doseTotal / volumeCapsula);
        var volumeTotalPorDose = volumeCapsula * divisaoDose;

        var quantidadeExcipientePorDose = volumeTotalPorDose - doseTotal;

        if (quantidadeExcipientePorDose < 0)
            throw new DomainException("Volume da cápsula não comporta a dose.");

        return new ResultadoCapsula(capsula, quantidadeExcipientePorDose, divisaoDose);
    }

    private static DateOnly AtualizarDataValidade(DateOnly dataValidadeItem, RegraDataValidade regra,
        DateOnly dataValidadeAtual, RegraDataValidade regraAtual, out RegraDataValidade novaRegra)
    {
        if (dataValidadeItem < dataValidadeAtual)
        {
            novaRegra = regra;
            return dataValidadeItem;
        }

        novaRegra = regraAtual;
        return dataValidadeAtual;
    }



    private static decimal ConverterParaUnEstoque(decimal quantidade, int unidadeMedidaId, Produto produto)
    {
        var conversao = ConversaoUnidadeMedidaCreator.Criar(
            (UnidadeMedidaAbreviacao)unidadeMedidaId,
            produto.UnidadeEstoqueId
        );

        return conversao.CalcularConversao(quantidade);
    }

    private static decimal ConverterDeUnEstoque(decimal quantidadeEstoque, int unidadeDestinoId, Produto produto)
    {
        var conversao = ConversaoUnidadeMedidaCreator.Criar(
            produto.UnidadeEstoqueId,
            (UnidadeMedidaAbreviacao)unidadeDestinoId
        );

        return conversao.CalcularConversao(quantidadeEstoque);
    }

    private static decimal ConverterParaMl(decimal quantidade, int unidadeMedidaId)
    {
        var conversao = ConversaoUnidadeMedidaCreator.Criar(
            (UnidadeMedidaAbreviacao)unidadeMedidaId,
            UnidadeMedidaAbreviacao.mL
        );

        return conversao.CalcularConversao(quantidade);
    }

    /// <summary>
    /// Serviço puro para cálculos financeiros relacionados a receitas manipuladas.
    /// Contém métodos estáticos sem efeitos colaterais para cálculos de valores de custo e venda.
    /// </summary>
    private static class CalculadorFinanceiro
    {
        /// <summary>
        /// Calcula os valores de custo e venda para um item baseado no rastreio e quantidade.
        /// </summary>
        /// <param name="rastreio">Rastreio do cálculo contendo informações do produto</param>
        /// <param name="quantidade">Quantidade calculada para o item</param>
        /// <returns>Tupla contendo valor de custo e valor de venda</returns>
        public static (decimal valorCusto, decimal valorVenda) CalcularValoresItem(
            ReceitaManipuladaRastreioCalculo rastreio,
            decimal quantidade)
        {
            var valorCustoItem = quantidade * rastreio.Produto.ValorCusto;
            var valorVendaItem = valorCustoItem + valorCustoItem * rastreio.Produto.MargemLucro / 100;
            return (valorCustoItem, valorVendaItem);
        }

        /// <summary>
        /// Atualiza os valores financeiros acumulados com os valores de um item específico.
        /// </summary>
        /// <param name="acumulador">Objeto acumulador de valores financeiros a ser atualizado</param>
        /// <param name="rastreio">Rastreio do cálculo contendo informações do produto</param>
        /// <param name="quantidade">Quantidade calculada para o item</param>
        public static void AtualizarValoresAcumulados(
            ValoresFinanceiros acumulador,
            ReceitaManipuladaRastreioCalculo rastreio,
            decimal quantidade)
        {
            var (valorCustoItem, valorVendaItem) = CalcularValoresItem(rastreio, quantidade);

            acumulador.ValorCustoTotal += valorCustoItem;
            acumulador.ValorVendaTotal += valorVendaItem;
            acumulador.ValorLucroTotal += valorVendaItem - valorCustoItem;
        }
    }

    private sealed class ValoresFinanceiros
    {
        public decimal ValorCustoTotal { get; set; }
        public decimal ValorVendaTotal { get; set; }
        public decimal ValorLucroTotal { get; set; }
    }

    private record struct ProcessamentoResultadoBaseReceita(
        decimal VolumeTotalMateriaPrimaMl,
        DateOnly DataValidade,
        RegraDataValidade RegraDataValidade,
        RegraEscolhaExcipiente RegraEscolhaExcipiente,
        decimal ValorCustoItem,
        decimal ValorVendaItem);

    private record struct ProcessamentoResultadoBaseQsp(
        decimal VolumeItemMl,
        decimal ValorCustoItem,
        decimal ValorVendaItem,
        DateOnly DataValidade,
        RegraDataValidade RegraDataValidade);

    public record struct ResultadoCapsula(
        ProdutoTipoCapsula Capsula,
        decimal QuantidadeExcipiente,
        decimal DivisaoDose);

    public record EmbalagemQspFracionada(
        ProdutoEmbalagem Embalagem,
        int Quantidade);

    #region Navigation properties

    public virtual ReceitaManipuladaBaseCalculo BaseCalculo { get; set; }
    public virtual ReceitaManipuladaValores Valores { get; set; }
    public virtual ICollection<ReceitaManipuladaCalculo> Calculos { get; set; } = new List<ReceitaManipuladaCalculo>();

    public virtual ICollection<ReceitaManipuladaRastreioCalculo> Rastreios { get; set; } =
        new List<ReceitaManipuladaRastreioCalculo>();

    public virtual ICollection<ReceitaManipuladaItem> Itens { get; set; } = new List<ReceitaManipuladaItem>();
    public virtual FormaFarmaceutica FormaFarmaceutica { get; set; }
    public virtual Cliente Paciente { get; set; }

    #endregion
}